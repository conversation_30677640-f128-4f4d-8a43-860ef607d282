/* This file was generated by the Hex-Rays decompiler version 7.7.0.220118.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: GNU C++
*/

#include <defs.h>


//-------------------------------------------------------------------------
// Function declarations

void (*init_proc())(void);
__int64 __fastcall sub_401290(); // weak
// __int64 __fastcall json_array_clear(_QWORD); weak
// __int64 __fastcall json_string(_QWORD); weak
// __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_create(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// __int64 __fastcall json_array_size(_QWORD); weak
// __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD); weak
// size_t strlen(const char *s);
// int strncmp(const char *s1, const char *s2, size_t n);
// __int64 __fastcall Password_Encode(_QWORD, _QWORD, _QWORD); weak
// char *strncpy(char *dest, const char *src, size_t n);
// __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall Get_Private_Profile_Integer(_QWORD, _QWORD, _QWORD, _QWORD); weak
// struct tm *localtime(const time_t *timer);
// int system(const char *command);
// __int64 __fastcall Get_Profile_String(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_integer(_QWORD); weak
// __int64 __fastcall Get_Private_Profile_String(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// time_t time(time_t *timer);
// const unsigned __int16 **__ctype_b_loc(void);
// __int64 __fastcall qs_io_read(_QWORD, _QWORD); weak
// void free(void *ptr);
// __int64 __fastcall json_array_get(_QWORD, _QWORD); weak
// __int64 __fastcall Password_Decode(_QWORD, _QWORD); weak
// __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD); weak
// __int64 json_array(void); weak
// __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall Get_Profile_Integer(_QWORD, _QWORD, _QWORD); weak
// int strcmp(const char *s1, const char *s2);
// __int64 strtol(const char *nptr, char **endptr, int base);
// __int64 json_delete(void); weak
// __int64 __fastcall qs_io_free(_QWORD); weak
// char *strtok(char *s, const char *delim);
// int snprintf(char *s, size_t maxlen, const char *format, ...);
// void *memmove(void *dest, const void *src, size_t n);
// __int64 __fastcall qs_io_printf(_QWORD, _QWORD); weak
// int __fastcall __libc_start_main(int (__fastcall *main)(int, char **, char **), int argc, char **ubp_av, void (*init)(void), void (*fini)(void), void (*rtld_fini)(void), void *stack_end);
// __int64 __gmon_start__(void); weak
// __int64 __fastcall Set_Profile_String(_QWORD, _QWORD, _QWORD); weak
// size_t fwrite(const void *ptr, size_t size, size_t n, FILE *s);
// __int64 __fastcall json_object_get(_QWORD, _QWORD); weak
// __int64 __fastcall qs_io_size(_QWORD); weak
// __int64 __fastcall json_array_append_new(_QWORD, _QWORD); weak
int __cdecl main(int argc, const char **argv, const char **envp);
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void));
signed __int64 sub_402E40();
void sub_402EC0();
__int64 sub_402F30();
__int64 __fastcall sub_402F60(__int64 a1, __int64 a2, __int64 a3);
void __fastcall sub_402F80(__int64 a1);
void _libc_csu_init(void); // idb
void _libc_csu_fini(void); // idb
void (*sub_403030())(void);
void term_proc();

//-------------------------------------------------------------------------
// Data declarations

_UNKNOWN unk_4032D1; // weak
_UNKNOWN unk_4032DC; // weak
_UNKNOWN unk_4032F5; // weak
_UNKNOWN unk_4033A1; // weak
__int64 qword_604000 = -1LL; // weak
__int64 qword_604010[] = { -1LL }; // weak
__int64 qword_604018 = 0LL; // weak
__int64 (*qword_604210)(void) = NULL; // weak
_UNKNOWN _bss_start; // weak
_UNKNOWN unk_60438F; // weak
FILE *stdout; // idb
FILE *stderr; // idb
char byte_6043C8; // weak
__int64 qword_6043D0; // weak
// extern _UNKNOWN _gmon_start__; weak


//----- (0000000000401268) ----------------------------------------------------
void (*init_proc())(void)
{
  if ( &_gmon_start__ )
    __gmon_start__();
  sub_402F30();
  return sub_403030();
}
// 401500: using guessed type __int64 __gmon_start__(void);

//----- (0000000000401290) ----------------------------------------------------
__int64 sub_401290()
{
  return qword_604210();
}
// 401290: using guessed type __int64 __fastcall sub_401290();
// 604210: using guessed type __int64 (*qword_604210)(void);

//----- (0000000000401560) ----------------------------------------------------
int __cdecl main(int argc, const char **argv, const char **envp)
{
  __int64 v3; // rbx
  int v4; // r15d
  unsigned int Private_Profile_Integer; // edx
  __int64 v6; // r13
  char *v7; // r14
  const unsigned __int16 **v8; // rbx
  int v9; // edx
  char *i; // rcx
  __int64 v11; // rsi
  __int64 v12; // rcx
  char *j; // rsi
  __int64 v14; // rcx
  __int64 v15; // r8
  __int64 v16; // r9
  __int64 v17; // rdx
  __int64 v18; // rbx
  __int64 v19; // rax
  __int64 v20; // rsi
  unsigned __int64 v21; // rbx
  __int64 v22; // r14
  __int64 v23; // rax
  __int64 v24; // r8
  __int64 v25; // rsi
  char v27; // bl
  const char *v28; // rax
  char *v29; // r13
  const char *v30; // rax
  char *v31; // r13
  __int64 v32; // rbx
  __int64 v33; // rax
  __int64 v34; // rax
  const char *v35; // rsi
  __int64 v36; // rdi
  __int64 v37; // rdx
  __int64 v38; // rax
  __int64 v39; // rbx
  char *v40; // rax
  char *v41; // rax
  int v42; // r12d
  __int64 v43; // rax
  __int64 v44; // rax
  int Profile_Integer; // eax
  const char *v46; // rdi
  __int64 v47; // rax
  int v48; // eax
  __int64 v49; // rax
  __int64 v50; // rsi
  char *v51; // rdi
  __int64 v52; // r14
  __int64 v53; // rdx
  __int64 v54; // r13
  __int64 v55; // rbx
  __int64 v56; // rax
  __int64 v57; // rsi
  unsigned __int64 v58; // rbx
  __int64 v59; // r12
  __int64 v60; // rax
  __int64 v61; // r8
  __int64 v62; // rax
  struct tm *v63; // rax
  const char *v64; // rax
  char *v65; // rbx
  __int64 v66; // rax
  __int64 v67; // rax
  __int64 v68; // r13
  __int64 v69; // r12
  __int64 v70; // rdx
  __int64 v71; // rbx
  __int64 v72; // rbx
  __int64 v73; // rax
  __int64 v74; // r14
  __int64 v75; // rax
  __int64 v76; // rax
  __int64 v77; // rax
  time_t *v78; // rdi
  __int64 v79; // rax
  __int64 v80; // rdx
  __int64 v81; // r12
  __int64 v82; // r9
  __int64 v83; // rax
  const char *v84; // rsi
  __int64 v85; // r9
  __int64 v86; // rdx
  __int64 v87; // rbx
  __int64 v88; // rax
  unsigned __int64 v89; // rax
  __int64 v90; // r13
  __int64 v91; // r12
  __int64 v92; // rdx
  __int64 v93; // rbx
  __int64 v94; // rbx
  __int64 v95; // rax
  __int64 v96; // rsi
  unsigned __int64 v97; // rbx
  __int64 v98; // rax
  __int64 v99; // r8
  __int64 v100; // rax
  __int64 v101; // rax
  __int64 v102; // r13
  __int64 v103; // rdx
  __int64 v104; // rbx
  char *v105; // [rsp+8h] [rbp-D00h]
  int v106; // [rsp+10h] [rbp-CF8h]
  __int64 v107; // [rsp+10h] [rbp-CF8h]
  __int64 v108; // [rsp+20h] [rbp-CE8h]
  __int64 v109; // [rsp+20h] [rbp-CE8h]
  __int64 v110; // [rsp+28h] [rbp-CE0h]
  __int64 v111; // [rsp+30h] [rbp-CD8h] BYREF
  __int64 v112[2]; // [rsp+40h] [rbp-CC8h] BYREF
  char command[256]; // [rsp+50h] [rbp-CB8h] BYREF
  char v114[272]; // [rsp+150h] [rbp-BB8h] BYREF
  __int64 v115[32]; // [rsp+260h] [rbp-AA8h] BYREF
  char v116; // [rsp+360h] [rbp-9A8h]
  char s[256]; // [rsp+370h] [rbp-998h] BYREF
  char v118; // [rsp+470h] [rbp-898h]
  char s1[256]; // [rsp+480h] [rbp-888h] BYREF
  char v120; // [rsp+580h] [rbp-788h]
  __int64 v121[32]; // [rsp+590h] [rbp-778h] BYREF
  char v122; // [rsp+690h] [rbp-678h]
  char v123[256]; // [rsp+6A0h] [rbp-668h] BYREF
  char v124; // [rsp+7A0h] [rbp-568h]
  char v125[256]; // [rsp+7B0h] [rbp-558h] BYREF
  char v126; // [rsp+8B0h] [rbp-458h]
  time_t timer[128]; // [rsp+8C0h] [rbp-448h] BYREF
  char v128; // [rsp+CC0h] [rbp-48h]

  v3 = qs_io_create(argc, argv, envp);
  v4 = qs_call("nc", "version", v3, 60LL, 0xFFFFFFFFLL);
  if ( v4 )
  {
    qs_io_free(v3);
    v4 = 1;
    fwrite("[Notification Center] daemon is not alive.\n", 1uLL, 0x2BuLL, stderr);
  }
  else
  {
    qs_io_free(v3);
    memset(v114, 0, 257);
    Get_Profile_String("Alert", "Migrated", "", v114, 256LL);
    if ( !v114[0] )
    {
      strcpy(command, "/usr/local/sbin/nc policy -c -t2 -AA009 -CC001");
      v112[0] = 0LL;
      memset(&command[47], 0, 0xD1uLL);
      memset(v115, 0, sizeof(v115));
      v116 = v4;
      Get_Profile_String("System", "Server Name", "", v115, 256LL);
      Private_Profile_Integer = Get_Private_Profile_Integer("SMS", "Using Provider", 0LL, "/etc/config/sms_config.conf");
      memset(s, 0, sizeof(s));
      v118 = v4;
      memset(s1, 0, sizeof(s1));
      v120 = v4;
      memset(v121, 0, sizeof(v121));
      v122 = v4;
      memset(v123, 0, sizeof(v123));
      v124 = v4;
      memset(v125, 0, sizeof(v125));
      v126 = v4;
      snprintf(s, 0x100uLL, "VOL%d", Private_Profile_Integer);
      if ( !(unsigned int)Get_Private_Profile_String(s, 4206795LL, "", s1, 256LL, "/etc/config/sms_config.conf") )
      {
LABEL_4:
        fwrite("[Notification Center] sms profile not supported.\n", 1uLL, 0x31uLL, stderr);
LABEL_5:
        v106 = 0;
        goto LABEL_6;
      }
      memset(timer, 0, 0x100uLL);
      LOBYTE(timer[32]) = v4;
      Get_Private_Profile_String(s, "url_type", 4207124LL, timer, 256LL, "/etc/config/sms_config.conf");
      Get_Private_Profile_String(s, "user", 4207124LL, v121, 256LL, "/etc/config/sms_config.conf");
      Get_Private_Profile_String(s, "enpassword", 4207124LL, v123, 256LL, "/etc/config/sms_config.conf");
      Get_Private_Profile_String(s, "other", 4207124LL, v125, 256LL, "/etc/config/sms_config.conf");
      if ( !strcmp(s1, "Clickatell") )
      {
        if ( !LOBYTE(v121[0]) || !v123[0] || !v125[0] )
          goto LABEL_4;
        v27 = 0;
      }
      else
      {
        if ( strncmp((const char *)timer, "http", 4uLL) )
          goto LABEL_4;
        if ( !v123[0] )
        {
          snprintf(v123, 0x100uLL, "%s", 0LL);
          v64 = (const char *)Password_Encode(v123, strlen(v123), 8LL);
          v65 = (char *)v64;
          if ( v64 )
          {
            snprintf(v123, 0x100uLL, "%s", v64);
            free(v65);
            goto LABEL_87;
          }
LABEL_108:
          fwrite("[Notification Center] sms profile convert fail.\n", 1uLL, 0x30uLL, stderr);
          goto LABEL_5;
        }
        v27 = 1;
      }
      v28 = (const char *)Password_Decode(v123, 4LL);
      v29 = (char *)v28;
      if ( v28 )
      {
        snprintf(v123, 0x100uLL, "%s", v28);
        free(v29);
        v30 = (const char *)Password_Encode(v123, strlen(v123), 8LL);
        v31 = (char *)v30;
        if ( v30 )
        {
          snprintf(v123, 0x100uLL, "%s", v30);
          free(v31);
          if ( !v27 )
          {
            v32 = json_pack("{ss ss ss ss sb ss}", "from", v115, "alias", "Clickatell - 1", "method");
            v33 = json_string(v121);
            json_object_set_new(v32, "user", v33);
            v34 = json_string(v125);
            v35 = "api_id";
            v36 = v32;
            json_object_set_new(v32, "api_id", v34);
            goto LABEL_88;
          }
LABEL_87:
          v32 = json_pack("{ss ss ss ss sb ss}", "from", v115, "alias", "Custom - 1", "method");
          v66 = json_string(v121);
          json_object_set_new(v32, "username", v66);
          v67 = json_string(timer);
          v35 = "url";
          v36 = v32;
          json_object_set_new(v32, "url", v67);
LABEL_88:
          v68 = qs_io_create(v36, v35, v37);
          json_dump_callback(v32, sub_402F60, v68, 0LL);
          sub_402F80(v32);
          if ( (unsigned int)qs_call("nc", "add_channel_sender_sms", v68, 60LL, 0xFFFFFFFFLL) )
          {
            qs_io_free(v68);
LABEL_90:
            fwrite("[Notification Center] sms migration fail.\n", 1uLL, 0x2AuLL, stderr);
            sub_402F80(v32);
            goto LABEL_5;
          }
          v72 = qs_io_size(v68);
          v73 = qs_io_read(v68, 0LL);
          v32 = json_loadb(v73, v72, 0LL, 0LL);
          if ( (unsigned int)json_unpack(v32, "{s[{sI}]}", "data", 4207717LL, v112) )
            goto LABEL_90;
          qs_io_free(v68);
          fwrite("[Notification Center] sms migrated.\n", 1uLL, 0x24uLL, stdout);
          sub_402F80(v32);
          v74 = json_array();
          memset(v123, 0, sizeof(v123));
          v124 = 0;
          if ( (unsigned int)Get_Private_Profile_String(
                               "SMS",
                               "Country_Code",
                               4207124LL,
                               v123,
                               256LL,
                               "/etc/config/sms_config.conf") )
          {
            if ( v123[0] == 43 )
            {
              memset(v125, 0, sizeof(v125));
              v126 = 0;
              memset(timer, 0, 257);
              if ( (int)Get_Private_Profile_String(
                          "SMS",
                          "Phone1",
                          4207124LL,
                          v125,
                          256LL,
                          "/etc/config/sms_config.conf") > 0 )
              {
                snprintf((char *)timer, 0x100uLL, "%s%s", v123, v125);
                v83 = json_pack("{ss ss}", "phone", timer, "country", v123, v82);
                json_array_append_new(v74, v83);
              }
              v84 = "Phone2";
              if ( (int)Get_Private_Profile_String(
                          "SMS",
                          "Phone2",
                          4207124LL,
                          v125,
                          256LL,
                          "/etc/config/sms_config.conf") > 0 )
              {
                snprintf((char *)timer, 0x100uLL, "%s%s", v123, v125);
                v84 = (const char *)json_pack("{ss ss}", "phone", timer, "country", v123, v85);
                json_array_append_new(v74, v84);
              }
              if ( json_array_size(v74) )
              {
                v90 = qs_io_create(v74, v84, v86);
                json_dump_callback(v74, sub_402F60, v90, 0LL);
                json_array_clear(v74);
                if ( !(unsigned int)qs_call("nc", "add_channel_receiver_sms", v90, 60LL, 0xFFFFFFFFLL) )
                {
                  v94 = qs_io_size(v90);
                  v95 = qs_io_read(v90, 0LL);
                  v96 = v94;
                  v97 = 0LL;
                  v109 = json_loadb(v95, v96, 0LL, 0LL);
                  v107 = json_object_get(v109, "data");
                  while ( v97 < json_array_size(v107) )
                  {
                    v98 = json_array_get(v107, v97);
                    if ( !v98 )
                      break;
                    v121[0] = 0LL;
                    json_unpack(v98, "{sI}", 4207717LL, v121, v99);
                    if ( v121[0] )
                    {
                      v100 = json_integer(v121[0]);
                      json_array_append_new(v74, v100);
                    }
                    ++v97;
                  }
                  qs_io_free(v90);
                  sub_402F80(v109);
                  if ( (unsigned int)Get_Profile_Integer("Alert", "SMS Mode", 0LL) )
                  {
                    v102 = json_pack(
                             "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss sI sO}]}",
                             4206818LL,
                             1LL,
                             4207678LL,
                             "Rule - 2",
                             "threshold");
                    v104 = qs_io_create(
                             "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss sI sO}]}",
                             4206818LL,
                             v103);
                    json_dump_callback(v102, sub_402F60, v104, 0LL);
                    if ( !(unsigned int)qs_call("nc", "add_policy_event", v104, 60LL, 0xFFFFFFFFLL) )
                      fwrite("[Notification Center] sms system-event-log rule migrated.\n", 1uLL, 0x3AuLL, stdout);
                    qs_io_free(v104);
                    sub_402F80(v102);
                  }
                  sub_402F80(v74);
                  if ( !(unsigned int)Get_Profile_Integer("Alert", "SMS_FW_UPDATE", 1LL) )
                    goto LABEL_5;
                  v106 = 2;
                  strcpy((char *)&v112[2] + strlen(command), " --sms");
LABEL_6:
                  v115[0] = 0LL;
                  v6 = json_array();
                  memset(timer, 0, 513);
                  if ( !(unsigned int)Get_Profile_String("Alert", "Alert Mail", "", timer, 512LL) )
                  {
                    v50 = 1LL;
                    fwrite("[Notification Center] mail not have any receiver.\n", 1uLL, 0x32uLL, stderr);
                    goto LABEL_65;
                  }
                  v7 = strtok((char *)timer, ",");
                  if ( v7 )
                  {
                    v8 = __ctype_b_loc();
                    do
                    {
                      v9 = strlen(v7);
                      for ( i = &v7[v9]; ; *i = 0 )
                      {
                        v11 = *--i;
                        if ( ((*v8)[v11] & 0x2000) == 0 )
                          break;
                        --v9;
                      }
                      v12 = *v7;
                      for ( j = v7; (_BYTE)v12; --v9 )
                      {
                        if ( ((*v8)[v12] & 0x2000) == 0 )
                          break;
                        v12 = *++j;
                      }
                      memmove(v7, j, v9 + 1);
                      if ( *v7 )
                      {
                        v38 = json_pack("{ss}", "email", v7, v14, v15, v16);
                        json_array_append_new(v6, v38);
                      }
                      v7 = strtok(0LL, ",");
                    }
                    while ( v7 );
                  }
                  if ( json_array_size(v6) )
                  {
                    v108 = qs_io_create(v6, ",", v17);
                    json_dump_callback(v6, sub_402F60, v108, 0LL);
                    json_array_clear(v6);
                    if ( (unsigned int)qs_call("nc", "add_channel_receiver_mail", v108, 60LL, 0xFFFFFFFFLL) )
                    {
                      qs_io_free(v108);
                      v50 = 1LL;
                      fwrite("[Notification Center] mail create receiver fail.\n", 1uLL, 0x31uLL, stderr);
                      goto LABEL_65;
                    }
                    v18 = qs_io_size(v108);
                    v19 = qs_io_read(v108, 0LL);
                    v20 = v18;
                    v21 = 0LL;
                    v110 = json_loadb(v19, v20, 0LL, 0LL);
                    v22 = json_object_get(v110, "data");
                    while ( v21 < json_array_size(v22) )
                    {
                      v23 = json_array_get(v22, v21);
                      if ( !v23 )
                        break;
                      timer[0] = 0LL;
                      json_unpack(v23, "{sI}", "id", timer, v24);
                      if ( timer[0] )
                      {
                        v25 = json_integer(timer[0]);
                        json_array_append_new(v6, v25);
                      }
                      ++v21;
                    }
                    qs_io_free(v108);
                    sub_402F80(v110);
                  }
                  v39 = json_pack("{si ss si sb sb}", 4206818LL, 1LL, "alias", "Migration - 1", "status");
                  memset(s, 0, sizeof(s));
                  v118 = 0;
                  memset(s1, 0, sizeof(s1));
                  v120 = 0;
                  if ( (int)Get_Private_Profile_String(
                              4207124LL,
                              "mailhub",
                              4207124LL,
                              s,
                              256LL,
                              "/etc/config/ssmtp/ssmtp.conf") <= 0 )
                    goto LABEL_63;
                  v40 = strtok(s, ":");
                  if ( v40 )
                    strncpy(s1, v40, 0x100uLL);
                  v41 = strtok(0LL, ":");
                  if ( !v41 || (v42 = strtol(v41, 0LL, 10)) == 0 || !s1[0] )
                  {
LABEL_63:
                    v50 = 1LL;
                    fwrite("[Notification Center] mail host/port not defined.\n", 1uLL, 0x32uLL, stderr);
                    goto LABEL_64;
                  }
                  v43 = json_string(s1);
                  json_object_set_new(v39, "host", v43);
                  v44 = json_integer(v42);
                  json_object_set_new(v39, "port", v44);
                  Profile_Integer = Get_Profile_Integer("Alert", "ProviderIdx", 0LL);
                  if ( Profile_Integer == 7 )
                  {
                    v46 = (const char *)&unk_4032D1;
                    goto LABEL_53;
                  }
                  if ( Profile_Integer > 7 )
                  {
                    if ( Profile_Integer == 12 )
                    {
                      v46 = "PChome Online";
                      goto LABEL_53;
                    }
                    if ( Profile_Integer <= 12 )
                    {
                      v46 = (const char *)&unk_4032DC;
                      if ( Profile_Integer == 8 )
                        goto LABEL_53;
                    }
                    else
                    {
                      v46 = (const char *)&unk_4032F5;
                      if ( Profile_Integer == 15 )
                        goto LABEL_53;
                      v46 = "Outlook.com";
                      if ( Profile_Integer == 38 )
                        goto LABEL_53;
                    }
                  }
                  else
                  {
                    if ( Profile_Integer == 4 )
                    {
                      v46 = "Yahoo";
                      goto LABEL_53;
                    }
                    if ( Profile_Integer > 4 )
                    {
                      v46 = "QQ";
                      if ( Profile_Integer != 5 )
                        v46 = "QQ VIP";
LABEL_53:
                      v47 = json_string(v46);
                      json_object_set_new(v39, "vendor", v47);
                      v111 = 0LL;
                      v112[0] = 0LL;
                      if ( (unsigned int)Get_Private_Profile_String(
                                           4207124LL,
                                           "UseTLS",
                                           "NO",
                                           &v111,
                                           8LL,
                                           "/etc/config/ssmtp/ssmtp.conf")
                        && (unsigned int)Get_Private_Profile_String(
                                           4207124LL,
                                           "UseSTARTTLS",
                                           "NO",
                                           v112,
                                           8LL,
                                           "/etc/config/ssmtp/ssmtp.conf") )
                      {
                        v48 = 0;
                        if ( (_BYTE)v111 == 89 && *(_WORD *)((char *)&v111 + 1) == 21317 && !BYTE3(v111) )
                        {
                          LOBYTE(v48) = 1;
                          if ( LOBYTE(v112[0]) == 89 && *(_WORD *)((char *)v112 + 1) == 21317 )
                            v48 = 2 - (BYTE3(v112[0]) != 0);
                        }
                        v49 = json_integer(v48);
                        json_object_set_new(v39, "secure", v49);
                        memset(v121, 0, sizeof(v121));
                        v122 = 0;
                        memset(v123, 0, sizeof(v123));
                        v124 = 0;
                        memset(v125, 0, sizeof(v125));
                        v126 = 0;
                        memset(timer, 0, sizeof(timer));
                        v128 = 0;
                        if ( (unsigned int)Get_Profile_String("Alert", "Sender", 4207124LL, v121, 256LL) )
                        {
                          v75 = json_string(v121);
                          json_object_set_new(v39, "user", v75);
                          v76 = json_string(v121);
                          json_object_set_new(v39, "email", v76);
                          if ( (int)Get_Private_Profile_String(
                                      4207124LL,
                                      "enAuthPass",
                                      4207124LL,
                                      v123,
                                      256LL,
                                      "/etc/config/ssmtp/ssmtp.conf") <= 0 )
                          {
                            if ( !(unsigned int)Get_Private_Profile_String(
                                                  4207124LL,
                                                  "AppID",
                                                  "M0Zq5M0jjtgALq6lVvpZki7C",
                                                  v125,
                                                  256LL,
                                                  "/etc/config/ssmtp/ssmtp.conf")
                              || !(unsigned int)Get_Private_Profile_String(
                                                  4207124LL,
                                                  "enRefreshToken",
                                                  4207124LL,
                                                  timer,
                                                  1024LL,
                                                  "/etc/config/ssmtp/ssmtp.conf") )
                            {
                              v50 = 1LL;
                              fwrite("[Notification Center] mail oauth config error.\n", 1uLL, 0x2FuLL, stderr);
                              goto LABEL_64;
                            }
                            v101 = json_integer(2LL);
                            json_object_set_new(v39, "auth", v101);
                            v78 = timer;
                          }
                          else
                          {
                            v77 = json_integer(1LL);
                            json_object_set_new(v39, "auth", v77);
                            v78 = (time_t *)v123;
                          }
                          v79 = json_string(v78);
                          json_object_set_new(v39, "secret", v79);
                          v81 = qs_io_create(v39, "secret", v80);
                          json_dump_callback(v39, sub_402F60, v81, 0LL);
                          if ( (unsigned int)qs_call("nc", "add_channel_sender_mail", v81, 60LL, 0xFFFFFFFFLL) )
                          {
                            qs_io_free(v81);
                          }
                          else
                          {
                            sub_402F80(v39);
                            v87 = qs_io_size(v81);
                            v88 = qs_io_read(v81, 0LL);
                            v39 = json_loadb(v88, v87, 0LL, 0LL);
                            if ( !(unsigned int)json_unpack(v39, "{s[{sI}]}", "data", 4207717LL, v115) )
                            {
                              qs_io_free(v81);
                              fwrite("[Notification Center] mail migrated.\n", 1uLL, 0x25uLL, stdout);
                              sub_402F80(v39);
                              Get_Profile_Integer("Alert", "Plain Text", 0LL);
                              if ( (unsigned int)Get_Profile_Integer("Alert", &unk_4033A1, 0LL) )
                              {
                                v91 = json_pack(
                                        "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss sI sO}]}",
                                        4206818LL,
                                        1LL,
                                        4207678LL,
                                        "Rule - 1",
                                        "threshold");
                                v93 = qs_io_create(
                                        "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss sI sO}]}",
                                        4206818LL,
                                        v92);
                                json_dump_callback(v91, sub_402F60, v93, 0LL);
                                if ( !(unsigned int)qs_call("nc", "add_policy_event", v93, 60LL, 0xFFFFFFFFLL) )
                                  fwrite(
                                    "[Notification Center] mail system-event-log rule migrated.\n",
                                    1uLL,
                                    0x3BuLL,
                                    stdout);
                                qs_io_free(v93);
                                sub_402F80(v91);
                              }
                              sub_402F80(v6);
                              v50 = (__int64)"SMTP_FW_UPDATE";
                              v51 = "Alert";
                              if ( (unsigned int)Get_Profile_Integer("Alert", "SMTP_FW_UPDATE", 1LL) )
                              {
                                v106 |= 1u;
                                v89 = strlen(command) + 1;
                                v51 = &command[v89];
                                *(__int64 *)((char *)&v112[1] + v89 + 7) = 0x6C69616D2D2D20LL;
                              }
LABEL_66:
                              v52 = json_array();
                              v54 = qs_io_create(v51, v50, v53);
                              qs_io_printf(v54, "{\"type\":8}");
                              if ( (unsigned int)qs_call("nc", "get_channel_receiver", v54, 60LL, 0xFFFFFFFFLL) )
                              {
                                qs_io_free(v54);
                                fwrite("[Notification Center] notify fetch receiver fail.\n", 1uLL, 0x32uLL, stderr);
                              }
                              else
                              {
                                v55 = qs_io_size(v54);
                                v56 = qs_io_read(v54, 0LL);
                                v57 = v55;
                                v58 = 0LL;
                                v105 = (char *)json_loadb(v56, v57, 0LL, 0LL);
                                v59 = json_object_get(v105, "data");
                                while ( v58 < json_array_size(v59) )
                                {
                                  v60 = json_array_get(v59, v58);
                                  if ( !v60 )
                                    break;
                                  timer[0] = 0LL;
                                  json_unpack(v60, "{sI}", 4207717LL, timer, v61);
                                  if ( timer[0] )
                                  {
                                    v62 = json_integer(timer[0]);
                                    json_array_append_new(v52, v62);
                                  }
                                  ++v58;
                                }
                                qs_io_free(v54);
                                sub_402F80((__int64)v105);
                                if ( json_array_size(v52) )
                                {
                                  if ( (unsigned int)Get_Profile_Integer("Push Notification", "Level", 0LL) )
                                  {
                                    v69 = json_pack(
                                            "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss si sO}]}",
                                            4206818LL,
                                            1LL,
                                            4207678LL,
                                            "Rule - 3",
                                            "threshold");
                                    v71 = qs_io_create(
                                            "{si ss si s[{si sb s[] si} {si sb s[] si} {si sb s[] si}] s[{si sb ss si sO}]}",
                                            4206818LL,
                                            v70);
                                    json_dump_callback(v69, sub_402F60, v71, 0LL);
                                    if ( !(unsigned int)qs_call("nc", "add_policy_event", v71, 60LL, 0xFFFFFFFFLL) )
                                      fwrite(
                                        "[Notification Center] notify system-event-log rule migrated.\n",
                                        1uLL,
                                        0x3DuLL,
                                        stdout);
                                    qs_io_free(v71);
                                    sub_402F80(v69);
                                  }
                                  sub_402F80(v52);
                                  if ( (unsigned int)Get_Profile_Integer("Push Notification", "PUSH_FW_UPDATE", 1LL) )
                                  {
                                    *(__int64 *)((char *)&v112[2] + strlen(command)) = 0x687375702D2D20LL;
                                    goto LABEL_82;
                                  }
LABEL_76:
                                  if ( !v106 )
                                  {
LABEL_77:
                                    timer[0] = 0LL;
                                    time(timer);
                                    v63 = localtime(timer);
                                    snprintf(
                                      v114,
                                      0x100uLL,
                                      "%04d/%02d/%02d %02d:%02d:%02d",
                                      (unsigned int)(v63->tm_year + 1900),
                                      (unsigned int)(v63->tm_mon + 1),
                                      (unsigned int)v63->tm_mday,
                                      (unsigned int)v63->tm_hour,
                                      (unsigned int)v63->tm_min,
                                      (unsigned int)v63->tm_sec);
                                    Set_Profile_String("Alert", "Migrated", v114);
                                    return v4;
                                  }
LABEL_82:
                                  system(command);
                                  goto LABEL_77;
                                }
                                fwrite("[Notification Center] notify not have any receiver.\n", 1uLL, 0x34uLL, stderr);
                              }
                              sub_402F80(v52);
                              goto LABEL_76;
                            }
                          }
                          v50 = 1LL;
                          fwrite("[Notification Center] mail migration fail.\n", 1uLL, 0x2BuLL, stderr);
                          goto LABEL_64;
                        }
                        v50 = 1LL;
                        fwrite("[Notification Center] mail sender not defined.\n", 1uLL, 0x2FuLL, stderr);
                      }
                      else
                      {
                        v50 = 1LL;
                        fwrite("[Notification Center] mail secure mode not defined.\n", 1uLL, 0x34uLL, stderr);
                      }
LABEL_64:
                      sub_402F80(v39);
LABEL_65:
                      v51 = (char *)v6;
                      sub_402F80(v6);
                      goto LABEL_66;
                    }
                    if ( Profile_Integer == 1 )
                    {
                      v46 = "Gmail";
                      goto LABEL_53;
                    }
                  }
                  v46 = "custom";
                  goto LABEL_53;
                }
                qs_io_free(v90);
                fwrite("[Notification Center] sms create receiver fail.\n", 1uLL, 0x30uLL, stderr);
              }
              else
              {
                fwrite("[Notification Center] sms not have any receiver.\n", 1uLL, 0x31uLL, stderr);
              }
            }
            else
            {
              fwrite("[Notification Center] sms country code error.\n", 1uLL, 0x2EuLL, stderr);
            }
          }
          else
          {
            fwrite("[Notification Center] sms country code not defined.\n", 1uLL, 0x34uLL, stderr);
          }
          sub_402F80(v74);
          v106 = 0;
          goto LABEL_6;
        }
      }
      goto LABEL_108;
    }
    fwrite("[Notification Center] system was migrated !\n", 1uLL, 0x2CuLL, stdout);
  }
  return v4;
}
// 401D88: conditional instruction was optimized away because eax.4==6
// 401BF7: variable 'v14' is possibly undefined
// 401BF7: variable 'v15' is possibly undefined
// 401BF7: variable 'v16' is possibly undefined
// 401859: variable 'v17' is possibly undefined
// 40192F: variable 'v24' is possibly undefined
// 401F16: variable 'v53' is possibly undefined
// 401FCF: variable 'v61' is possibly undefined
// 402270: variable 'v37' is possibly undefined
// 4023BE: variable 'v70' is possibly undefined
// 40267F: variable 'v80' is possibly undefined
// 4027C4: variable 'v82' is possibly undefined
// 402836: variable 'v85' is possibly undefined
// 40298E: variable 'v86' is possibly undefined
// 402B14: variable 'v92' is possibly undefined
// 402BFB: variable 'v99' is possibly undefined
// 402DA6: variable 'v103' is possibly undefined
// 4012A0: using guessed type __int64 __fastcall json_array_clear(_QWORD);
// 4012B0: using guessed type __int64 __fastcall json_string(_QWORD);
// 4012C0: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 4012D0: using guessed type __int64 __fastcall qs_io_create(_QWORD, _QWORD, _QWORD);
// 4012F0: using guessed type __int64 __fastcall json_array_size(_QWORD);
// 401300: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 401330: using guessed type __int64 __fastcall Password_Encode(_QWORD, _QWORD, _QWORD);
// 401350: using guessed type __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD);
// 401370: using guessed type __int64 __fastcall Get_Private_Profile_Integer(_QWORD, _QWORD, _QWORD, _QWORD);
// 4013A0: using guessed type __int64 __fastcall Get_Profile_String(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 4013B0: using guessed type __int64 __fastcall json_integer(_QWORD);
// 4013C0: using guessed type __int64 __fastcall Get_Private_Profile_String(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 4013F0: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 401410: using guessed type __int64 __fastcall json_array_get(_QWORD, _QWORD);
// 401420: using guessed type __int64 __fastcall Password_Decode(_QWORD, _QWORD);
// 401430: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 401440: using guessed type __int64 json_array(void);
// 401450: using guessed type __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 401460: using guessed type __int64 __fastcall Get_Profile_Integer(_QWORD, _QWORD, _QWORD);
// 4014A0: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 4014E0: using guessed type __int64 __fastcall qs_io_printf(_QWORD, _QWORD);
// 401510: using guessed type __int64 __fastcall Set_Profile_String(_QWORD, _QWORD, _QWORD);
// 401530: using guessed type __int64 __fastcall json_object_get(_QWORD, _QWORD);
// 401540: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 401550: using guessed type __int64 __fastcall json_array_append_new(_QWORD, _QWORD);

//----- (0000000000402E10) ----------------------------------------------------
// positive sp value has been detected, the output may be wrong!
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void))
{
  __int64 v3; // rax
  int v4; // esi
  __int64 v5; // [rsp-8h] [rbp-8h] BYREF
  char *retaddr; // [rsp+0h] [rbp+0h] BYREF

  v4 = v5;
  v5 = v3;
  __libc_start_main(
    (int (__fastcall *)(int, char **, char **))main,
    v4,
    &retaddr,
    _libc_csu_init,
    _libc_csu_fini,
    a3,
    &v5);
  __halt();
}
// 402E16: positive sp value 8 has been found
// 402E1D: variable 'v3' is possibly undefined

//----- (0000000000402E40) ----------------------------------------------------
signed __int64 sub_402E40()
{
  signed __int64 result; // rax

  result = &unk_60438F - &_bss_start;
  if ( (unsigned __int64)(&unk_60438F - &_bss_start) > 0xE )
    return 0LL;
  return result;
}

//----- (0000000000402EC0) ----------------------------------------------------
void sub_402EC0()
{
  __int64 v0; // rax
  unsigned __int64 i; // rbx

  if ( !byte_6043C8 )
  {
    v0 = qword_6043D0;
    for ( i = &qword_604018 - qword_604010 - 1; qword_6043D0 < i; v0 = qword_6043D0 )
    {
      qword_6043D0 = v0 + 1;
      ((void (*)(void))qword_604010[v0 + 1])();
    }
    sub_402E40();
    byte_6043C8 = 1;
  }
}
// 604010: using guessed type __int64 qword_604010[];
// 604018: using guessed type __int64 qword_604018;
// 6043C8: using guessed type char byte_6043C8;
// 6043D0: using guessed type __int64 qword_6043D0;

//----- (0000000000402F30) ----------------------------------------------------
__int64 sub_402F30()
{
  return 0LL;
}

//----- (0000000000402F60) ----------------------------------------------------
__int64 __fastcall sub_402F60(__int64 a1, __int64 a2, __int64 a3)
{
  return (unsigned int)qs_io_write(a3, a1, a2) - (unsigned int)a2;
}
// 401360: using guessed type __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD);

//----- (0000000000402F80) ----------------------------------------------------
void __fastcall sub_402F80(__int64 a1)
{
  __int64 v1; // rax
  __int64 v2; // rax

  if ( a1 )
  {
    v1 = *(_QWORD *)(a1 + 8);
    if ( v1 != -1 )
    {
      v2 = v1 - 1;
      *(_QWORD *)(a1 + 8) = v2;
      if ( !v2 )
        json_delete();
    }
  }
}
// 401490: using guessed type __int64 json_delete(void);

//----- (0000000000402FB0) ----------------------------------------------------
void _libc_csu_init(void)
{
  init_proc();
}

//----- (0000000000403030) ----------------------------------------------------
void (*sub_403030())(void)
{
  void (**v0)(void); // rbx
  void (*result)(void); // rax

  v0 = (void (**)(void))&qword_604000;
  result = (void (*)(void))qword_604000;
  if ( qword_604000 != -1 )
  {
    do
    {
      --v0;
      result();
      result = *v0;
    }
    while ( *v0 != (void (*)(void))-1LL );
  }
  return result;
}
// 604000: using guessed type __int64 qword_604000;

//----- (0000000000403068) ----------------------------------------------------
void term_proc()
{
  sub_402EC0();
}

// nfuncs=101 queued=12 decompiled=12 lumina nreq=0 worse=0 better=0
// ALL OK, 12 function(s) have been successfully decompiled
