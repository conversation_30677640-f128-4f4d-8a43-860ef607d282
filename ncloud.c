/* This file was generated by the Hex-Rays decompiler version 7.7.0.220118.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: GNU C++
*/

#include <defs.h>

#include <stdarg.h>


//-------------------------------------------------------------------------
// Function declarations

void (*init_proc())(void);
__int64 __fastcall sub_402120(); // weak
// int *__errno_location(void);
// __int64 __fastcall __strdup(_QWORD); weak
// __int64 __fastcall qs_stop(_QWORD); weak
// __int64 __fastcall json_object_get_boolean(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_tokener_parse_ex(_QWORD, _QWORD, _QWORD); weak
// __pid_t fork(void);
// __int64 __fastcall json_object_new_string(_QWORD); weak
// FILE *popen(const char *command, const char *modes);
// __int64 __fastcall qcloud_system_get_source_id(_QWORD); weak
// __int64 __fastcall qcloud_system_get_receiver_device_list(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qcloud_get_device_info(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_create(_QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 json_object_new_array(void); weak
// int ftruncate64(int fd, __off64_t length);
// __int64 __fastcall json_tokener_free(_QWORD); weak
// __int64 __fastcall json_object_put(_QWORD); weak
// char *strncpy(char *dest, const char *src, size_t n);
// __int64 __fastcall qcloud_system_update_receiver_device(_QWORD, _QWORD); weak
// void *memset(void *s, int c, size_t n);
// void __noreturn abort(void);
// int dprintf(int fd, const char *fmt, ...);
// __int64 json_tokener_new(void); weak
// __int64 __fastcall qcloud_system_delete_receiver_device(_QWORD); weak
// int fcntl(int fd, int cmd, ...);
// void *calloc(size_t nmemb, size_t size);
// __int64 __fastcall qcloud_system_get_receiver_device(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// int sscanf(const char *s, const char *format, ...);
// __int64 __fastcall json_object_to_json_string_ext(_QWORD, _QWORD); weak
// __pid_t waitpid(__pid_t pid, int *stat_loc, int options);
// __int64 __fastcall json_object_get_object(_QWORD); weak
// __int64 __fastcall json_tokener_get_error(_QWORD); weak
// int fsync(int fd);
// FILE *freopen64(const char *filename, const char *modes, FILE *stream);
// __int64 __fastcall qs_signal(_QWORD, _QWORD); weak
// __int64 __fastcall json_object_array_add(_QWORD, _QWORD); weak
// __int64 __fastcall qs_io_read(_QWORD, _QWORD); weak
// void free(void *ptr);
// __int64 __fastcall json_object_get_string(_QWORD); weak
// void __noreturn exit(int status);
// __int64 __fastcall json_object_new_object(_QWORD); weak
// int strcmp(const char *s1, const char *s2);
// __int64 strtol(const char *nptr, char **endptr, int base);
// int pclose(FILE *stream);
// int __xstat64(int ver, const char *filename, struct stat64 *stat_buf);
// int fprintf(FILE *stream, const char *format, ...);
// ssize_t read(int fd, void *buf, size_t nbytes);
// __pid_t getpid(void);
// int chdir(const char *path);
// __int64 __fastcall qs_io_free(_QWORD); weak
// __int64 __fastcall qcloud_system_get_app_id(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_free(_QWORD); weak
// __int64 __fastcall json_object_get_string_len(_QWORD); weak
// __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD); weak
// char *strtok(char *s, const char *delim);
// char *fgets(char *s, int n, FILE *stream);
// __int64 __fastcall qid_sys_get_qid_device_id(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// void *realloc(void *ptr, size_t size);
// __int64 __fastcall qcloud_system_add_receiver_device(_QWORD, _QWORD); weak
// int open64(const char *file, int oflag, ...);
// __int64 qs_io_printf(_QWORD, const char *, ...); weak
// __mode_t umask(__mode_t mask);
// __int64 __fastcall json_object_new_boolean(_QWORD); weak
// __int64 qs_io_alive(void); weak
// __int64 __fastcall qs_io_realloc(_QWORD, _QWORD); weak
// int vdprintf(int fd, const char *fmt, __gnuc_va_list arg);
// int __fastcall __libc_start_main(int (__fastcall *main)(int, char **, char **), int argc, char **ubp_av, void (*init)(void), void (*fini)(void), void (*rtld_fini)(void), void *stack_end);
// __int64 __fastcall qs_start(_QWORD); weak
// int flock(int fd, int operation);
// __int64 __gmon_start__(void); weak
// void qsort(void *base, size_t nmemb, size_t size, __compar_fn_t compar);
// __int64 __fastcall qs_reply(_QWORD); weak
// size_t fwrite(const void *ptr, size_t size, size_t n, FILE *s);
// __int64 __fastcall json_object_new_int(_QWORD); weak
// int close(int fd);
// __int64 __fastcall json_object_get(_QWORD); weak
// __int64 __fastcall qs_io_size(_QWORD); weak
// __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_object_get_type(_QWORD); weak
int __cdecl main(int argc, const char **argv, const char **envp);
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void));
signed __int64 sub_4028C0();
void sub_402940();
__int64 sub_4029B0();
__int64 __fastcall sub_4029E0(__int64 a1, __int64 a2, int a3);
__int64 __fastcall sub_402A00(); // weak
int __fastcall sub_402A10(__int64 a1, void (__fastcall *a2)(__int64, __int64, __int64, _QWORD), __int64 a3, __int64 a4, int a5);
int sub_402B60(int a1, __int64 a2, ...);
void __fastcall auto_free_str(void **a1);
int __fastcall auto_close_fd(int *a1);
__int64 __fastcall qs_reply_json(__int64 a1, int a2, __int64 a3, unsigned int a4, char *a5);
int compar(const void *, const void *); // idb
int __fastcall sub_402DF0(__int64 a1, void (__fastcall *a2)(__int64, __int64, __int64, _QWORD), __int64 a3, __int64 a4, int a5);
void __fastcall sub_402F50(_QWORD *a1);
__int64 __fastcall sub_403280(__int64 a1, __int64 a2, __int64 a3);
void __fastcall qs_rest_destroy(_DWORD *ptr);
const char *__fastcall qs_rest_endpoint(const char **a1, const char *a2, const char *a3, const char *a4, const char *a5);
_DWORD *qs_rest_create();
__int64 __fastcall qs_rest_dispatch(__int64 a1, __int64 a2, __int64 a3, int a4);
__int64 __fastcall sub_4039E0(__int64 a1);
void __fastcall ncloud_misc_get_cloud_info(__int64 a1, __int64 a2, __int64 a3, int a4);
__int64 __fastcall ncloud_system_add_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4);
__int64 __fastcall ncloud_system_delete_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4);
__int64 __fastcall ncloud_system_update_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4);
__int64 __fastcall ncloud_system_get_receiver_device_list(__int64 a1, __int64 a2, __int64 a3, int a4);
void _libc_csu_init(void); // idb
void _libc_csu_fini(void); // idb
void (*sub_404F10())(void);
void term_proc();

//-------------------------------------------------------------------------
// Data declarations

__int64 qword_606080 = -1LL; // weak
__int64 qword_606090[] = { -1LL }; // weak
__int64 qword_606098 = 0LL; // weak
__int64 (*qword_6062A0)(void) = NULL; // weak
char *_ncloud_system_update_receiver_device__ = "\n[Options]:\n   enabled=(bool)\n   pair_id=(str)\n   receiver_app_id=(str)\n   receiver_reg_id=(str)\n   receiver_locale=(str)\n   receiver_user_id=(str)\n   receiver_os_type=(str)\n   receiver_user_type=(str)\n   receiver_os_version=(str)\n   receiver_app_version=(str)\n   receiver_device_name=(str)\n   receiver_device_type=(str)\n"; // weak
char *_ncloud_system_delete_receiver_device__ = "\n[Options]:\n   pair_id=(str)\n"; // weak
char *_ncloud_system_add_receiver_device__ = "\n[Options]:\n   enabled=(bool)\n   receiver_app_id=(str)\n   receiver_reg_id=(str)\n   receiver_locale=(str)\n   receiver_user_id=(str)\n   receiver_os_type=(str)\n   receiver_user_type=(str)\n   receiver_os_version=(str)\n   receiver_app_version=(str)\n   receiver_device_name=(str)\n   receiver_device_type=(str)\n"; // weak
char *_ncloud_misc_get_cloud_info__ = "\n[Options]:\n   os_type=(str)           chrome, ...\n"; // weak
_UNKNOWN _bss_start; // weak
_UNKNOWN unk_60654F; // weak
FILE *stdout; // idb
FILE *stdin; // idb
FILE *stderr; // idb
char byte_606588; // weak
__int64 qword_606590; // weak
__int64 qword_606598; // weak
void *ptr; // idb
__int64 _ncloud_system_get_receiver_device_list__; // weak
// extern _UNKNOWN _gmon_start__; weak


//----- (00000000004020F0) ----------------------------------------------------
void (*init_proc())(void)
{
  if ( &_gmon_start__ )
    __gmon_start__();
  sub_4029B0();
  return sub_404F10();
}
// 402570: using guessed type __int64 __gmon_start__(void);

//----- (0000000000402120) ----------------------------------------------------
__int64 sub_402120()
{
  return qword_6062A0();
}
// 402120: using guessed type __int64 __fastcall sub_402120();
// 6062A0: using guessed type __int64 (*qword_6062A0)(void);

//----- (0000000000402610) ----------------------------------------------------
int __cdecl main(int argc, const char **argv, const char **envp)
{
  __pid_t v3; // eax
  __pid_t v4; // eax
  int v5; // eax
  int v6; // ebx
  unsigned int v7; // eax
  _BYTE v9[56]; // [rsp+0h] [rbp-38h] BYREF

  v3 = fork();
  if ( v3 < 0 )
LABEL_10:
    exit(1);
  if ( v3 )
    goto LABEL_11;
  v4 = fork();
  if ( v4 < 0 )
    goto LABEL_10;
  if ( v4 )
LABEL_11:
    exit(0);
  freopen64("/dev/null", "r", stdin);
  freopen64("/dev/null", "w", stdout);
  freopen64("/dev/null", "w", stderr);
  chdir("/");
  umask(0);
  v5 = open64("/var/lock/ncloud.pid", 66, 420LL);
  v6 = v5;
  if ( v5 == -1 )
  {
    fprintf(stderr, "NCloud create pid file %s fail.\n", "/var/lock/ncloud.pid");
    goto LABEL_10;
  }
  if ( flock(v5, 6) == -1 )
  {
    memset(v9, 0, 0x21uLL);
    read(v6, v9, 0x20uLL);
    fprintf(stderr, "NCloud daemon is running (%s).\n", v9);
    goto LABEL_10;
  }
  if ( ftruncate64(v6, 0LL) == -1 )
  {
    fwrite("NCloud write pid to file fail.\n", 1uLL, 0x1FuLL, stderr);
    exit(1);
  }
  v7 = getpid();
  sub_402B60(v6, (__int64)"%d", v7);
  ptr = qs_rest_create();
  qs_rest_endpoint(
    (const char **)ptr,
    (const char *)ncloud_misc_get_cloud_info,
    "misc.get_cloud_info",
    (const char *)sub_402A10,
    _ncloud_misc_get_cloud_info__);
  qs_rest_endpoint(
    (const char **)ptr,
    (const char *)ncloud_system_add_receiver_device,
    "system.add_receiver_device",
    (const char *)sub_402A10,
    _ncloud_system_add_receiver_device__);
  qs_rest_endpoint(
    (const char **)ptr,
    (const char *)ncloud_system_delete_receiver_device,
    "system.delete_receiver_device",
    (const char *)sub_402A10,
    _ncloud_system_delete_receiver_device__);
  qs_rest_endpoint(
    (const char **)ptr,
    (const char *)ncloud_system_update_receiver_device,
    "system.update_receiver_device",
    (const char *)sub_402A10,
    _ncloud_system_update_receiver_device__);
  qs_rest_endpoint(
    (const char **)ptr,
    (const char *)ncloud_system_get_receiver_device_list,
    "system.get_receiver_device_list",
    (const char *)sub_402A10,
    (const char *)_ncloud_system_get_receiver_device_list__);
  qs_signal(2LL, sub_402A00);
  qs_signal(15LL, sub_402A00);
  qword_606598 = qs_create("@/var/run/ncloud", 4LL, 1LL, sub_4029E0);
  qs_start(qword_606598);
  qs_free(qword_606598);
  qs_rest_destroy(ptr);
  flock(v6, 8);
  close(v6);
  return 0;
}
// 4021E0: using guessed type __int64 __fastcall qs_create(_QWORD, _QWORD, _QWORD, _QWORD);
// 402340: using guessed type __int64 __fastcall qs_signal(_QWORD, _QWORD);
// 402450: using guessed type __int64 __fastcall qs_free(_QWORD);
// 402550: using guessed type __int64 __fastcall qs_start(_QWORD);
// 402A00: using guessed type __int64 __fastcall sub_402A00();
// 606528: using guessed type char *_ncloud_system_update_receiver_device__;
// 606530: using guessed type char *_ncloud_system_delete_receiver_device__;
// 606538: using guessed type char *_ncloud_system_add_receiver_device__;
// 606540: using guessed type char *_ncloud_misc_get_cloud_info__;
// 606598: using guessed type __int64 qword_606598;
// 6065A8: using guessed type __int64 _ncloud_system_get_receiver_device_list__;

//----- (0000000000402890) ----------------------------------------------------
// positive sp value has been detected, the output may be wrong!
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void))
{
  __int64 v3; // rax
  int v4; // esi
  __int64 v5; // [rsp-8h] [rbp-8h] BYREF
  char *retaddr; // [rsp+0h] [rbp+0h] BYREF

  v4 = v5;
  v5 = v3;
  __libc_start_main(
    (int (__fastcall *)(int, char **, char **))main,
    v4,
    &retaddr,
    _libc_csu_init,
    _libc_csu_fini,
    a3,
    &v5);
  __halt();
}
// 402896: positive sp value 8 has been found
// 40289D: variable 'v3' is possibly undefined

//----- (00000000004028C0) ----------------------------------------------------
signed __int64 sub_4028C0()
{
  signed __int64 result; // rax

  result = &unk_60654F - &_bss_start;
  if ( (unsigned __int64)(&unk_60654F - &_bss_start) > 0xE )
    return 0LL;
  return result;
}

//----- (0000000000402940) ----------------------------------------------------
void sub_402940()
{
  __int64 v0; // rax
  unsigned __int64 i; // rbx

  if ( !byte_606588 )
  {
    v0 = qword_606590;
    for ( i = &qword_606098 - qword_606090 - 1; qword_606590 < i; v0 = qword_606590 )
    {
      qword_606590 = v0 + 1;
      ((void (*)(void))qword_606090[v0 + 1])();
    }
    sub_4028C0();
    byte_606588 = 1;
  }
}
// 606090: using guessed type __int64 qword_606090[];
// 606098: using guessed type __int64 qword_606098;
// 606588: using guessed type char byte_606588;
// 606590: using guessed type __int64 qword_606590;

//----- (00000000004029B0) ----------------------------------------------------
__int64 sub_4029B0()
{
  return 0LL;
}

//----- (00000000004029E0) ----------------------------------------------------
__int64 __fastcall sub_4029E0(__int64 a1, __int64 a2, int a3)
{
  return qs_rest_dispatch((__int64)ptr, a1, a2, a3);
}

//----- (0000000000402A00) ----------------------------------------------------
__int64 sub_402A00()
{
  return qs_stop(qword_606598);
}
// 402150: using guessed type __int64 __fastcall qs_stop(_QWORD);
// 402A00: using guessed type __int64 __fastcall sub_402A00();
// 606598: using guessed type __int64 qword_606598;

//----- (0000000000402A10) ----------------------------------------------------
int __fastcall sub_402A10(
        __int64 a1,
        void (__fastcall *a2)(__int64, __int64, __int64, _QWORD),
        __int64 a3,
        __int64 a4,
        int a5)
{
  int v9; // eax
  __int64 v11; // rdi
  __int64 *v12; // r15
  __int64 v13; // rdi
  __int64 v14; // r12
  __int64 v15; // rax
  unsigned int v16; // r12d
  __int64 v17; // rax
  __int64 v18; // r12
  __int64 v19; // [rsp+0h] [rbp-58h]
  __int64 v20; // [rsp+8h] [rbp-50h]
  int v21[15]; // [rsp+1Ch] [rbp-3Ch] BYREF

  v21[0] = a5;
  v9 = fork();
  if ( v9 >= 0 )
  {
    if ( !v9 )
    {
      if ( !fork() )
      {
        v11 = a3;
        if ( !qs_io_size(a3) )
          goto LABEL_9;
        v20 = json_tokener_new();
        do
        {
          v16 = qs_io_size(a3);
          v17 = qs_io_read(a3, 0LL);
          v18 = json_tokener_parse_ex(v20, v17, v16);
        }
        while ( (unsigned int)json_tokener_get_error(v20) == 1 );
        v11 = v20;
        v19 = v18;
        json_tokener_free(v20);
        if ( !v18 )
LABEL_9:
          v19 = json_object_new_object(v11);
        v12 = *(__int64 **)(json_object_get_object(a4) + 40);
        while ( v12 )
        {
          v13 = v12[1];
          v14 = *v12;
          v12 = (__int64 *)v12[2];
          v15 = json_object_get(v13);
          json_object_object_add(v19, v14, v15);
        }
        qs_io_realloc(a3, 0LL);
        a2(a1, a3, v19, (unsigned int)a5);
        json_object_put(v19);
      }
      exit(0);
    }
    waitpid(v9, 0LL, 0);
  }
  if ( a3 )
    qs_io_free(a3);
  return auto_close_fd(v21);
}
// 402170: using guessed type __int64 __fastcall json_tokener_parse_ex(_QWORD, _QWORD, _QWORD);
// 402210: using guessed type __int64 __fastcall json_tokener_free(_QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402280: using guessed type __int64 json_tokener_new(void);
// 402300: using guessed type __int64 __fastcall json_object_get_object(_QWORD);
// 402310: using guessed type __int64 __fastcall json_tokener_get_error(_QWORD);
// 402360: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402430: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 402520: using guessed type __int64 __fastcall qs_io_realloc(_QWORD, _QWORD);
// 4025D0: using guessed type __int64 __fastcall json_object_get(_QWORD);
// 4025E0: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);
// 402A10: using guessed type unsigned int var_3C[15];

//----- (0000000000402B60) ----------------------------------------------------
int sub_402B60(int a1, __int64 a2, ...)
{
  gcc_va_list arg; // [rsp+8h] [rbp-D0h] BYREF

  va_start(arg, a2);
  return vdprintf(a1, "%d", arg);
}

//----- (0000000000402C00) ----------------------------------------------------
void __fastcall auto_free_str(void **a1)
{
  void *v2; // rdi
  void **v3; // rbx

  if ( a1 )
  {
    v2 = *a1;
    if ( v2 )
    {
      v3 = a1;
      free(v2);
      *v3 = 0LL;
    }
  }
}

//----- (0000000000402C30) ----------------------------------------------------
int __fastcall auto_close_fd(int *a1)
{
  int *v2; // rax
  int *v3; // rbp
  int v4; // edi

  v2 = __errno_location();
  v3 = v2;
  *v2 = 0;
  if ( a1 )
  {
    v4 = *a1;
    if ( v4 >= 0 )
    {
      LODWORD(v2) = fcntl(v4, 1);
      if ( !(_DWORD)v2 || *v3 != 9 )
        LODWORD(v2) = close(*a1);
    }
  }
  return (int)v2;
}

//----- (0000000000402C80) ----------------------------------------------------
__int64 __fastcall qs_reply_json(__int64 a1, int a2, __int64 a3, unsigned int a4, char *a5)
{
  __int64 result; // rax
  __int64 v10; // r12
  const char *v11; // rax
  __int64 v12; // rax
  char *v13; // rdi
  __int64 v14; // rax
  const char *v15; // rax

  result = qs_io_alive();
  if ( (_BYTE)result )
  {
    v10 = a3;
    if ( a4 )
    {
      v10 = json_object_new_object(a1);
      v12 = json_object_new_int(a4);
      json_object_object_add(v10, "error", v12);
      v13 = "";
      if ( a5 )
        v13 = a5;
      v14 = json_object_new_string(v13);
      json_object_object_add(v10, "message", v14);
      qs_io_realloc(a1, 0LL);
      if ( a2 >= 0 )
        goto LABEL_4;
    }
    else
    {
      qs_io_realloc(a1, 0LL);
      if ( a2 >= 0 )
      {
LABEL_4:
        dprintf(a2, "Content-Type: application/json; charset=utf-8\r\n\r\n");
        v11 = (const char *)json_object_to_json_string_ext(v10, 0LL);
        dprintf(a2, "%s", v11);
        fsync(a2);
        goto LABEL_5;
      }
    }
    v15 = (const char *)json_object_to_json_string_ext(v10, 0LL);
    qs_io_printf(a1, "%s", v15);
LABEL_5:
    result = qs_reply(a1);
    if ( v10 != a3 )
      return json_object_put(v10);
  }
  return result;
}
// 402190: using guessed type __int64 __fastcall json_object_new_string(_QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 4022E0: using guessed type __int64 __fastcall json_object_to_json_string_ext(_QWORD, _QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 4024E0: using guessed type __int64 qs_io_printf(_QWORD, const char *, ...);
// 402510: using guessed type __int64 qs_io_alive(void);
// 402520: using guessed type __int64 __fastcall qs_io_realloc(_QWORD, _QWORD);
// 402590: using guessed type __int64 __fastcall qs_reply(_QWORD);
// 4025B0: using guessed type __int64 __fastcall json_object_new_int(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);

//----- (0000000000402DB0) ----------------------------------------------------
int __fastcall compar(const void *a1, const void *a2)
{
  int result; // eax

  if ( *((_BYTE *)a1 + 24) )
    return *((_BYTE *)a2 + 24) ^ 1;
  result = -1;
  if ( !*((_BYTE *)a2 + 24) )
    return strcmp(*((const char **)a1 + 1), *((const char **)a2 + 1));
  return result;
}

//----- (0000000000402DF0) ----------------------------------------------------
int __fastcall sub_402DF0(
        __int64 a1,
        void (__fastcall *a2)(__int64, __int64, __int64, _QWORD),
        __int64 a3,
        __int64 a4,
        int a5)
{
  __int64 v8; // rdi
  __int64 v9; // rbx
  __int64 *v10; // r12
  __int64 v11; // r15
  __int64 v12; // rdi
  __int64 v13; // rax
  int *v14; // rax
  int *v15; // rbx
  __int64 v16; // r12
  unsigned int v17; // ebx
  __int64 v18; // rax

  v8 = a3;
  if ( !qs_io_size(a3) )
    goto LABEL_2;
  v16 = json_tokener_new();
  do
  {
    v17 = qs_io_size(a3);
    v18 = qs_io_read(a3, 0LL);
    v9 = json_tokener_parse_ex(v16, v18, v17);
  }
  while ( (unsigned int)json_tokener_get_error(v16) == 1 );
  v8 = v16;
  json_tokener_free(v16);
  if ( !v9 )
LABEL_2:
    v9 = json_object_new_object(v8);
  v10 = *(__int64 **)(json_object_get_object(a4) + 40);
  while ( v10 )
  {
    v11 = *v10;
    v12 = v10[1];
    v10 = (__int64 *)v10[2];
    v13 = json_object_get(v12);
    json_object_object_add(v9, v11, v13);
  }
  qs_io_realloc(a3, 0LL);
  if ( a2 )
    a2(a1, a3, v9, (unsigned int)a5);
  json_object_put(v9);
  v14 = __errno_location();
  v15 = v14;
  *v14 = 0;
  if ( a5 >= 0 )
  {
    LODWORD(v14) = fcntl(a5, 1);
    if ( !(_DWORD)v14 || *v15 != 9 )
      LODWORD(v14) = close(a5);
  }
  if ( a3 )
    LODWORD(v14) = qs_io_free(a3);
  return (int)v14;
}
// 402170: using guessed type __int64 __fastcall json_tokener_parse_ex(_QWORD, _QWORD, _QWORD);
// 402210: using guessed type __int64 __fastcall json_tokener_free(_QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402280: using guessed type __int64 json_tokener_new(void);
// 402300: using guessed type __int64 __fastcall json_object_get_object(_QWORD);
// 402310: using guessed type __int64 __fastcall json_tokener_get_error(_QWORD);
// 402360: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402430: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 402520: using guessed type __int64 __fastcall qs_io_realloc(_QWORD, _QWORD);
// 4025D0: using guessed type __int64 __fastcall json_object_get(_QWORD);
// 4025E0: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);

//----- (0000000000402F50) ----------------------------------------------------
void __fastcall sub_402F50(_QWORD *a1)
{
  _QWORD *v1; // rax
  void *v2; // rdi
  char *v3; // rax
  void *v4; // rdi
  char *v5; // rax
  void *v6; // rdi
  char *v7; // rax
  void *v8; // rdi
  char *v9; // r14
  void *v10; // rdi
  char *v11; // r15
  void *v12; // rdi
  char *v13; // rbp
  void *v14; // rdi
  char *v15; // rbx
  void *v16; // rdi
  char *v17; // r12
  void *v18; // rdi
  __int64 v19; // r13
  __int64 v20; // rdi
  void *v21; // rdi
  _BYTE *v22; // rdi
  _BYTE *v23; // rdi
  _BYTE *v24; // rdi
  _BYTE *v25; // rdi
  _BYTE *v26; // rdi
  _BYTE *v27; // rdi
  _BYTE *v28; // rdi
  _BYTE *v29; // rdi
  char *v30; // [rsp+0h] [rbp-58h]
  char *v31; // [rsp+8h] [rbp-50h]
  char *v32; // [rsp+10h] [rbp-48h]

  v1 = a1;
  v2 = (void *)a1[1];
  if ( v2 )
  {
    free(v2);
    v1 = a1;
  }
  v30 = (char *)v1[7];
  if ( v1[4] )
  {
    do
    {
      v3 = v30;
      v4 = (void *)*((_QWORD *)v30 + 1);
      if ( v4 )
      {
        free(v4);
        v3 = v30;
      }
      v31 = (char *)*((_QWORD *)v3 + 7);
      if ( *((_QWORD *)v3 + 4) )
      {
        do
        {
          v5 = v31;
          v6 = (void *)*((_QWORD *)v31 + 1);
          if ( v6 )
          {
            free(v6);
            v5 = v31;
          }
          v32 = (char *)*((_QWORD *)v5 + 7);
          if ( *((_QWORD *)v5 + 4) )
          {
            do
            {
              v7 = v32;
              v8 = (void *)*((_QWORD *)v32 + 1);
              if ( v8 )
              {
                free(v8);
                v7 = v32;
              }
              v9 = (char *)*((_QWORD *)v7 + 7);
              if ( *((_QWORD *)v7 + 4) )
              {
                do
                {
                  v10 = (void *)*((_QWORD *)v9 + 1);
                  if ( v10 )
                    free(v10);
                  v11 = (char *)*((_QWORD *)v9 + 7);
                  if ( *((_QWORD *)v9 + 4) )
                  {
                    do
                    {
                      v12 = (void *)*((_QWORD *)v11 + 1);
                      if ( v12 )
                        free(v12);
                      v13 = (char *)*((_QWORD *)v11 + 7);
                      if ( *((_QWORD *)v11 + 4) )
                      {
                        do
                        {
                          v14 = (void *)*((_QWORD *)v13 + 1);
                          if ( v14 )
                            free(v14);
                          v15 = (char *)*((_QWORD *)v13 + 7);
                          if ( *((_QWORD *)v13 + 4) )
                          {
                            do
                            {
                              v16 = (void *)*((_QWORD *)v15 + 1);
                              if ( v16 )
                                free(v16);
                              v17 = (char *)*((_QWORD *)v15 + 7);
                              if ( *((_QWORD *)v15 + 4) )
                              {
                                do
                                {
                                  v18 = (void *)*((_QWORD *)v17 + 1);
                                  if ( v18 )
                                    free(v18);
                                  v19 = *((_QWORD *)v17 + 7);
                                  if ( *((_QWORD *)v17 + 4) )
                                  {
                                    do
                                    {
                                      v20 = v19;
                                      v19 += 64LL;
                                      sub_402F50(v20);
                                      v21 = (void *)*((_QWORD *)v17 + 7);
                                    }
                                    while ( (unsigned __int64)((v19 - (__int64)v21) >> 6) < *((_QWORD *)v17 + 4) );
                                  }
                                  else
                                  {
                                    v21 = (void *)*((_QWORD *)v17 + 7);
                                  }
                                  if ( v21 )
                                    free(v21);
                                  v22 = (_BYTE *)*((_QWORD *)v15 + 7);
                                  v17 += 64;
                                }
                                while ( (unsigned __int64)((v17 - v22) >> 6) < *((_QWORD *)v15 + 4) );
                              }
                              else
                              {
                                v22 = (_BYTE *)*((_QWORD *)v15 + 7);
                              }
                              if ( v22 )
                                free(v22);
                              v23 = (_BYTE *)*((_QWORD *)v13 + 7);
                              v15 += 64;
                            }
                            while ( (unsigned __int64)((v15 - v23) >> 6) < *((_QWORD *)v13 + 4) );
                          }
                          else
                          {
                            v23 = (_BYTE *)*((_QWORD *)v13 + 7);
                          }
                          if ( v23 )
                            free(v23);
                          v24 = (_BYTE *)*((_QWORD *)v11 + 7);
                          v13 += 64;
                        }
                        while ( (unsigned __int64)((v13 - v24) >> 6) < *((_QWORD *)v11 + 4) );
                      }
                      else
                      {
                        v24 = (_BYTE *)*((_QWORD *)v11 + 7);
                      }
                      if ( v24 )
                        free(v24);
                      v25 = (_BYTE *)*((_QWORD *)v9 + 7);
                      v11 += 64;
                    }
                    while ( (unsigned __int64)((v11 - v25) >> 6) < *((_QWORD *)v9 + 4) );
                  }
                  else
                  {
                    v25 = (_BYTE *)*((_QWORD *)v9 + 7);
                  }
                  if ( v25 )
                    free(v25);
                  v9 += 64;
                  v26 = (_BYTE *)*((_QWORD *)v32 + 7);
                }
                while ( (unsigned __int64)((v9 - v26) >> 6) < *((_QWORD *)v32 + 4) );
              }
              else
              {
                v26 = (_BYTE *)*((_QWORD *)v7 + 7);
              }
              if ( v26 )
                free(v26);
              v32 += 64;
              v27 = (_BYTE *)*((_QWORD *)v31 + 7);
            }
            while ( (unsigned __int64)((v32 - v27) >> 6) < *((_QWORD *)v31 + 4) );
          }
          else
          {
            v27 = (_BYTE *)*((_QWORD *)v5 + 7);
          }
          if ( v27 )
            free(v27);
          v31 += 64;
          v28 = (_BYTE *)*((_QWORD *)v30 + 7);
        }
        while ( (unsigned __int64)((v31 - v28) >> 6) < *((_QWORD *)v30 + 4) );
      }
      else
      {
        v28 = (_BYTE *)*((_QWORD *)v3 + 7);
      }
      if ( v28 )
        free(v28);
      v30 += 64;
      v29 = (_BYTE *)a1[7];
    }
    while ( (unsigned __int64)((v30 - v29) >> 6) < a1[4] );
  }
  else
  {
    v29 = (_BYTE *)v1[7];
  }
  if ( v29 )
    free(v29);
}

//----- (0000000000403280) ----------------------------------------------------
__int64 __fastcall sub_403280(__int64 a1, __int64 a2, __int64 a3)
{
  unsigned __int64 v3; // rdx
  __int64 v4; // rbx
  __int64 v5; // rcx
  const char *string; // rax
  const char *v8; // r15
  unsigned __int64 v9; // r14
  __int64 v10; // rdx
  unsigned __int64 i; // r13
  unsigned __int64 v12; // rbx
  __int64 v13; // rbp
  int v14; // eax
  const char *v15; // rdx
  __int64 v16; // [rsp+8h] [rbp-50h]
  __int64 v17[8]; // [rsp+18h] [rbp-40h] BYREF

  v17[0] = 0LL;
  json_object_object_get_ex(a3, "method", v17);
  if ( (unsigned int)json_object_get_type(v17[0]) == 6
    && (string = (const char *)json_object_get_string(v17[0]), (v8 = string) != 0LL)
    && *string
    && strcmp(string, "help") )
  {
    v9 = *(_QWORD *)(a1 + 32);
    v10 = *(_QWORD *)(a1 + 56);
    for ( i = 0LL; v9 > i; i = v12 + 1 )
    {
      while ( 1 )
      {
        v12 = (v9 + i) >> 1;
        v13 = v10 + (v12 << 6);
        if ( !*(_BYTE *)(v13 + 24) )
        {
          v16 = v10;
          v14 = strcmp(v8, *(const char **)(v13 + 8));
          v10 = v16;
          if ( v14 >= 0 )
            break;
        }
        v9 = (v9 + i) >> 1;
        if ( v12 <= i )
          return qs_reply(a2);
      }
      if ( !v14 )
      {
        if ( *(_QWORD *)(v13 + 40) )
        {
          v15 = *(const char **)(v13 + 16);
          if ( !v15 )
            v15 = "author slacks off ...";
          qs_io_printf(a2, "%s", v15);
        }
        return qs_reply(a2);
      }
    }
  }
  else
  {
    qs_io_printf(a2, "avaiable methods:\n");
    v3 = *(_QWORD *)(a1 + 32);
    v4 = *(_QWORD *)(a1 + 56);
    v5 = v4;
    if ( v3 )
    {
      do
      {
        if ( !*(_BYTE *)(v4 + 24) && *(_QWORD *)(v4 + 40) )
        {
          qs_io_printf(a2, "  %s\n", *(const char **)(v4 + 8));
          v5 = *(_QWORD *)(a1 + 56);
          v3 = *(_QWORD *)(a1 + 32);
        }
        v4 += 64LL;
      }
      while ( (v4 - v5) >> 6 < v3 );
    }
  }
  return qs_reply(a2);
}
// 402380: using guessed type __int64 __fastcall json_object_get_string(_QWORD);
// 402470: using guessed type __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD);
// 4024E0: using guessed type __int64 qs_io_printf(_QWORD, const char *, ...);
// 402590: using guessed type __int64 __fastcall qs_reply(_QWORD);
// 402600: using guessed type __int64 __fastcall json_object_get_type(_QWORD);
// 403280: using guessed type __int64 var_40[8];

//----- (0000000000403410) ----------------------------------------------------
void __fastcall qs_rest_destroy(_DWORD *ptr)
{
  if ( !ptr || *ptr != 1382372180 )
  {
    fprintf(stderr, "[ABORT] @%s() cannot destroy incorrect qs_rest %p\n", "qs_rest_destroy", ptr);
    abort();
  }
  sub_402F50(ptr);
  free(ptr);
}

//----- (0000000000403450) ----------------------------------------------------
const char *__fastcall qs_rest_endpoint(
        const char **a1,
        const char *a2,
        const char *a3,
        const char *a4,
        const char *a5)
{
  int v5; // r15d
  char *v7; // rax
  char *v8; // r14
  char *v9; // r9
  char *v10; // rax
  unsigned __int64 v11; // r14
  unsigned __int64 v12; // r13
  int v13; // ebp
  const char *v14; // rcx
  char *v15; // r15
  unsigned __int64 v16; // rbx
  const char **v17; // r12
  void *v18; // r9
  const char *v19; // r14
  char *v20; // rax
  char *v21; // rbx
  __int64 v22; // rax
  size_t v23; // rsi
  char *v24; // rdi
  unsigned __int64 v25; // r13
  unsigned __int64 v26; // r14
  const char *v27; // r12
  unsigned __int64 v28; // rbx
  const char *v29; // r13
  const char *result; // rax
  int v31; // eax
  char *v32; // rdi
  const char *v33; // r14
  __int64 v34; // rax
  int v35; // eax
  char *s1; // [rsp+0h] [rbp-78h]
  char *s1a; // [rsp+0h] [rbp-78h]
  const char **v38; // [rsp+8h] [rbp-70h]
  const char *v39; // [rsp+10h] [rbp-68h]
  char *ptr; // [rsp+18h] [rbp-60h]

  ptr = (char *)__strdup(a3);
  v7 = strtok(ptr, " /");
  if ( v7 )
  {
    v8 = v7;
    do
    {
      v9 = (char *)a1[7];
      v38 = a1;
      v39 = a1[4];
      v10 = v8;
      LOBYTE(v5) = *v8 == 58;
      v11 = (unsigned __int64)v39;
      v12 = 0LL;
      v13 = v5;
      v14 = v10;
      v15 = v9;
      while ( v12 < v11 )
      {
        if ( (_BYTE)v13 )
        {
          v16 = (v12 + v11) >> 1;
          v17 = (const char **)&v15[64 * v16];
          if ( *((_BYTE *)v17 + 24) == 1 )
            goto LABEL_25;
        }
        else
        {
          while ( 1 )
          {
            v16 = (v11 + v12) >> 1;
            v17 = (const char **)&v15[64 * v16];
            if ( !*((_BYTE *)v17 + 24) )
            {
              s1a = (char *)v14;
              v31 = strcmp(v14, v17[1]);
              v14 = s1a;
              if ( v31 >= 0 )
                break;
            }
            v11 = (v11 + v12) >> 1;
            if ( v12 >= v16 )
              goto LABEL_8;
          }
          if ( !v31 )
          {
LABEL_25:
            v32 = (char *)v17[1];
            v5 = v13;
            v33 = v14;
            a1 = v17;
            if ( v32 )
              free(v32);
            v34 = __strdup(v33);
            *((_BYTE *)v17 + 24) = v5;
            v17[1] = (const char *)v34;
            goto LABEL_15;
          }
        }
        v12 = v16 + 1;
      }
LABEL_8:
      v18 = v15;
      v5 = v13;
      v19 = v14;
      v38[4] = v39 + 1;
      v20 = (char *)realloc(v18, (_QWORD)(v39 + 1) << 6);
      v38[7] = v20;
      if ( !v20 )
      {
        fprintf(stderr, "[ABORT] @%s() create %s pattern %s failure\n", "qs_rest_endpoint", a3, v19);
        abort();
      }
      v21 = &v20[64 * (_QWORD)v38[4] - 64];
      memset(v21, 0, 0x40uLL);
      *(_DWORD *)v21 = 1382372180;
      v22 = __strdup(v19);
      v23 = (size_t)v38[4];
      v24 = (char *)v38[7];
      *((_QWORD *)v21 + 1) = v22;
      v21[24] = v13;
      qsort(v24, v23, 0x40uLL, compar);
      v25 = (unsigned __int64)v38[4];
      s1 = (char *)v19;
      v26 = 0LL;
      v27 = v38[7];
      while ( v25 > v26 )
      {
        if ( (_BYTE)v5 )
        {
          v28 = (v25 + v26) >> 1;
          a1 = (const char **)&v27[64 * v28];
          v35 = *((_BYTE *)a1 + 24) ^ 1;
        }
        else
        {
          while ( 1 )
          {
            v28 = (v25 + v26) >> 1;
            a1 = (const char **)&v27[64 * v28];
            if ( !*((_BYTE *)a1 + 24) )
            {
              v35 = strcmp(s1, a1[1]);
              if ( v35 >= 0 )
                break;
            }
            v25 = (v25 + v26) >> 1;
            if ( v28 <= v26 )
              goto LABEL_14;
          }
        }
        if ( !v35 )
          goto LABEL_15;
        v26 = v28 + 1;
      }
LABEL_14:
      a1 = 0LL;
LABEL_15:
      v8 = strtok(0LL, " /");
    }
    while ( v8 );
  }
  if ( ptr )
    free(ptr);
  v29 = a4;
  a1[5] = a2;
  if ( !a4 )
    v29 = (const char *)sub_402DF0;
  result = a5;
  a1[6] = v29;
  a1[2] = a5;
  return result;
}
// 4034C6: variable 'v5' is possibly undefined
// 402140: using guessed type __int64 __fastcall __strdup(_QWORD);

//----- (0000000000403740) ----------------------------------------------------
_DWORD *qs_rest_create()
{
  _DWORD *v0; // rbx

  v0 = calloc(1uLL, 0x40uLL);
  memset(v0, 0, 0x40uLL);
  *v0 = 1382372180;
  qs_rest_endpoint((const char **)v0, (const char *)sub_403280, "help", 0LL, 0LL);
  return v0;
}

//----- (0000000000403780) ----------------------------------------------------
__int64 __fastcall qs_rest_dispatch(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  char *v4; // rbx
  __int64 v5; // r15
  __int64 v6; // r14
  unsigned __int64 v7; // r13
  unsigned __int64 v8; // r12
  unsigned __int64 v9; // rbp
  unsigned __int64 i; // rax
  unsigned __int64 v11; // rax
  __int64 v12; // rsi
  __int64 (__fastcall *v13)(__int64, __int64, __int64, __int64, _QWORD); // rax
  __int64 result; // rax
  int v15; // eax
  __int64 v16; // rax
  int *v17; // rbx
  unsigned __int64 v18; // [rsp+0h] [rbp-68h]
  char *ptr; // [rsp+8h] [rbp-60h]
  __int64 v20; // [rsp+18h] [rbp-50h]

  v20 = json_object_new_object(a1);
  ptr = (char *)__strdup(a2);
  v4 = strtok(ptr, " /");
  if ( v4 && a1 )
  {
    v5 = a1;
    while ( 2 )
    {
      v6 = *(_QWORD *)(v5 + 56);
      v7 = 0LL;
      v18 = *(_QWORD *)(v5 + 32);
      v8 = v18;
      while ( 1 )
      {
        if ( v7 >= v8 )
        {
LABEL_8:
          for ( i = 0LL; v18 > i; i = v11 + 1 )
          {
            v11 = (v18 + i) >> 1;
            v5 = v6 + (v11 << 6);
            if ( *(_BYTE *)(v5 + 24) )
            {
              v16 = json_object_new_string(v4);
              json_object_object_add(v20, *(_QWORD *)(v5 + 8) + 1LL, v16);
              goto LABEL_23;
            }
          }
          v5 = 0LL;
          strtok(0LL, " /");
          if ( !ptr )
            goto LABEL_26;
          goto LABEL_13;
        }
        while ( 1 )
        {
          v9 = (v8 + v7) >> 1;
          v5 = v6 + (v9 << 6);
          if ( !*(_BYTE *)(v5 + 24) )
          {
            v15 = strcmp(v4, *(const char **)(v5 + 8));
            if ( v15 >= 0 )
              break;
          }
          v8 = (v8 + v7) >> 1;
          if ( v9 <= v7 )
            goto LABEL_8;
        }
        if ( !v15 )
          break;
        v7 = v9 + 1;
      }
LABEL_23:
      v4 = strtok(0LL, " /");
      if ( v4 )
        continue;
      break;
    }
    if ( !ptr )
    {
      v12 = *(_QWORD *)(v5 + 40);
      if ( v12 )
        goto LABEL_16;
      goto LABEL_26;
    }
  }
  else
  {
    v5 = a1;
    if ( !ptr )
      goto LABEL_14;
  }
LABEL_13:
  free(ptr);
LABEL_14:
  if ( v5 )
  {
    v12 = *(_QWORD *)(v5 + 40);
    if ( v12 )
    {
LABEL_16:
      v13 = *(__int64 (__fastcall **)(__int64, __int64, __int64, __int64, _QWORD))(v5 + 48);
      if ( v13 )
      {
        result = v13(a1, v12, a3, v20, (unsigned int)a4);
        if ( !v20 )
          return result;
        return json_object_put(v20);
      }
    }
  }
LABEL_26:
  v17 = __errno_location();
  *v17 = 0;
  if ( a4 >= 0 && (!fcntl(a4, 1) || *v17 != 9) )
    close(a4);
  qs_io_realloc(a3, 0LL);
  qs_io_free(a3);
  result = v20;
  if ( v20 )
    return json_object_put(v20);
  return result;
}
// 402140: using guessed type __int64 __fastcall __strdup(_QWORD);
// 402190: using guessed type __int64 __fastcall json_object_new_string(_QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402430: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 402520: using guessed type __int64 __fastcall qs_io_realloc(_QWORD, _QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);

//----- (00000000004039E0) ----------------------------------------------------
__int64 __fastcall sub_4039E0(__int64 a1)
{
  __int64 v1; // rbp
  __int64 v2; // rax
  __int64 v3; // rax
  __int64 v4; // rax
  __int64 v5; // rax
  __int64 v6; // rax
  __int64 v7; // rax
  __int64 v8; // rax
  __int64 v9; // rax
  __int64 v10; // rax
  __int64 v11; // rax
  __int64 v12; // rax
  __int64 v13; // rax
  __int64 v14; // rax
  __int64 v15; // rax
  __int64 v16; // rax
  __int64 v17; // rax
  __int64 v18; // rax
  __int64 v19; // rax
  __int64 v20; // rax

  v1 = json_object_new_object(a1);
  v2 = json_object_new_boolean(*(_DWORD *)(a1 + 708) == 1);
  json_object_object_add(v1, "enabled", v2);
  v3 = json_object_new_string(a1 + 868);
  json_object_object_add(v1, "receiver_type", v3);
  v4 = json_object_new_string(a1 + 463);
  json_object_object_add(v1, "receiver_app_id", v4);
  v5 = json_object_new_string(a1);
  json_object_object_add(v1, "receiver_reg_id", v5);
  v6 = json_object_new_string(a1 + 859);
  json_object_object_add(v1, "receiver_locale", v6);
  v7 = json_object_new_string(a1 + 777);
  json_object_object_add(v1, "receiver_user_id", v7);
  v8 = json_object_new_string(a1 + 430);
  json_object_object_add(v1, "receiver_os_type", v8);
  v9 = json_object_new_string(a1 + 842);
  json_object_object_add(v1, "receiver_user_type", v9);
  v10 = json_object_new_string(a1 + 674);
  json_object_object_add(v1, "receiver_os_version", v10);
  v11 = json_object_new_string(a1 + 528);
  json_object_object_add(v1, "receiver_app_version", v11);
  v12 = json_object_new_string(a1 + 301);
  json_object_object_add(v1, "receiver_device_name", v12);
  v13 = json_object_new_string(a1 + 545);
  json_object_object_add(v1, "receiver_device_type", v13);
  v14 = json_object_new_string(a1 + 892);
  json_object_object_add(v1, "pair_id", v14);
  v15 = json_object_new_string(a1 + 1345);
  json_object_object_add(v1, 4215295LL, v15);
  v16 = json_object_new_string(a1 + 1215);
  json_object_object_add(v1, "created_at", v16);
  v17 = json_object_new_string(a1 + 1280);
  json_object_object_add(v1, "updated_at", v17);
  v18 = json_object_new_int(*(unsigned int *)(a1 + 1412));
  json_object_object_add(v1, "reg_id_status", v18);
  v19 = json_object_new_string(a1 + 957);
  json_object_object_add(v1, "receiver_app_name", v19);
  v20 = json_object_new_string(a1 + 1086);
  json_object_object_add(v1, "receiver_app_display_name", v20);
  return v1;
}
// 402190: using guessed type __int64 __fastcall json_object_new_string(_QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402500: using guessed type __int64 __fastcall json_object_new_boolean(_QWORD);
// 4025B0: using guessed type __int64 __fastcall json_object_new_int(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);

//----- (0000000000403C10) ----------------------------------------------------
void __fastcall ncloud_misc_get_cloud_info(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  char *v5; // r8
  __int64 string; // rax
  FILE *v7; // rax
  unsigned int v8; // ecx
  char *v9; // r8
  FILE *v10; // rax
  FILE *v11; // rax
  int source_id; // eax
  int app_id; // eax
  int v14; // eax
  __int64 v15; // rdx
  int qid_device_id; // eax
  __int64 v17; // rdx
  __int64 v18; // r14
  __int64 v19; // rax
  __int64 v20; // rax
  __int64 v21; // rax
  FILE *v22; // rdi
  __int64 v23; // rax
  __int64 v24; // rax
  __int64 v25; // rax
  unsigned int v26; // eax
  __int64 v27; // rax
  __int64 v28; // rax
  __int64 v29; // rax
  __int64 v30[32]; // [rsp+0h] [rbp-1290h] BYREF
  char v31; // [rsp+100h] [rbp-1190h]
  FILE *stream; // [rsp+118h] [rbp-1178h]
  __int64 v33; // [rsp+120h] [rbp-1170h] BYREF
  __int64 v34; // [rsp+128h] [rbp-1168h] BYREF
  char v35[80]; // [rsp+130h] [rbp-1160h] BYREF
  char v36[80]; // [rsp+180h] [rbp-1110h] BYREF
  char v37[80]; // [rsp+1D0h] [rbp-10C0h] BYREF
  struct stat64 stat_buf; // [rsp+220h] [rbp-1070h] BYREF
  char v39[248]; // [rsp+2B0h] [rbp-FE0h] BYREF
  int v40; // [rsp+3A8h] [rbp-EE8h]
  __int16 v41; // [rsp+3ACh] [rbp-EE4h]
  char v42; // [rsp+3AEh] [rbp-EE2h]
  char v43[248]; // [rsp+3B0h] [rbp-EE0h] BYREF
  int v44; // [rsp+4A8h] [rbp-DE8h]
  __int16 v45; // [rsp+4ACh] [rbp-DE4h]
  char v46; // [rsp+4AEh] [rbp-DE2h]
  char v47[272]; // [rsp+4B0h] [rbp-DE0h] BYREF
  char v48[272]; // [rsp+5C0h] [rbp-CD0h] BYREF
  char v49[2944]; // [rsp+6D0h] [rbp-BC0h] BYREF
  int v50; // [rsp+1250h] [rbp-40h]

  v33 = 0LL;
  v34 = 0LL;
  json_object_object_get_ex(a3, 4215312LL, &v34);
  if ( !v34 || (unsigned int)json_object_get_type(v34) != 6 )
  {
    v5 = "os_type parameter incorrect";
LABEL_4:
    qs_reply_json(a2, a4, 0LL, 1u, v5);
LABEL_5:
    auto_free_str((void **)&v33);
    return;
  }
  string = json_object_get_string(v34);
  v33 = __strdup(string);
  memset(v47, 0, 257);
  memset(v39, 0, sizeof(v39));
  v40 = 0;
  v41 = 0;
  v42 = 0;
  memset(v43, 0, sizeof(v43));
  v45 = 0;
  v44 = 0;
  v46 = 0;
  memset(v30, 0, sizeof(v30));
  v31 = 0;
  v7 = popen("/usr/local/bin/qcloud_cli -t", "r");
  v8 = (unsigned int)v7;
  v9 = "get token fail";
  if ( !v7
    || (stream = v7,
        fgets((char *)v30, 256, v7),
        sscanf((const char *)v30, "%s", v47),
        pclose(stream),
        v10 = popen("/usr/sbin/get_qcloud_host -n notification", "r"),
        v8 = (unsigned int)v10,
        v9 = "get notification DNS fail",
        !v10) )
  {
LABEL_16:
    LOBYTE(v8) = 1;
    qs_reply_json(a2, a4, 0LL, v8, v9);
    goto LABEL_5;
  }
  stream = v10;
  fgets((char *)v30, 254, v10);
  sscanf((const char *)v30, "%s", v43);
  pclose(stream);
  v11 = popen("/usr/sbin/get_qcloud_host -n connector", "r");
  v8 = (unsigned int)v11;
  if ( !v11 )
  {
    v9 = "get connector DNS fail";
    goto LABEL_16;
  }
  stream = v11;
  fgets((char *)v30, 254, v11);
  sscanf((const char *)v30, "%s", v39);
  pclose(stream);
  memset(v35, 0, 65);
  source_id = qcloud_system_get_source_id(v35);
  v5 = "get source id fail";
  if ( source_id )
    goto LABEL_4;
  memset(v36, 0, 65);
  app_id = qcloud_system_get_app_id("im", "", v36, 0LL, "get source id fail");
  v5 = "get im app id fail";
  if ( app_id )
    goto LABEL_4;
  memset(v37, 0, 65);
  v14 = qcloud_system_get_app_id("browser", v33, v37, 0LL, "get im app id fail");
  v5 = "get browser app id fail";
  if ( v14 )
    goto LABEL_4;
  memset(v48, 0, 257);
  qid_device_id = qid_sys_get_qid_device_id(v48, 256LL, v15, 0LL, "get browser app id fail");
  v5 = "get device id fail";
  if ( qid_device_id )
    goto LABEL_4;
  memset(v49, 0, sizeof(v49));
  v50 = 0;
  if ( (unsigned int)qcloud_get_device_info(0LL, v49, v17, 0LL, "get device id fail") )
  {
    v5 = "get qcloud status fail";
    goto LABEL_4;
  }
  memset(&stat_buf, 0, sizeof(stat_buf));
  LODWORD(stream) = __xstat64(1, "/etc/IS_G", &stat_buf);
  v18 = json_object_new_object(1LL);
  v19 = json_object_new_boolean((_DWORD)stream == 0);
  json_object_object_add(v18, "generic", v19);
  stream = (FILE *)json_object_new_object(v18);
  json_object_object_add(v18, "server", stream);
  v20 = json_object_new_string(v39);
  json_object_object_add(stream, "connector", v20);
  v21 = json_object_new_string(v43);
  v22 = stream;
  json_object_object_add(stream, "notification", v21);
  stream = (FILE *)json_object_new_object(v22);
  json_object_object_add(v18, 4215247LL, stream);
  v23 = json_object_new_string(v36);
  json_object_object_add(stream, "im", v23);
  v24 = json_object_new_string(v37);
  json_object_object_add(stream, "browser", v24);
  v25 = json_object_new_string(v47);
  json_object_object_add(v18, "token", v25);
  v26 = strtol(&v49[255], 0LL, 10);
  v27 = json_object_new_int(v26);
  json_object_object_add(v18, 4215459LL, v27);
  v28 = json_object_new_string(v35);
  json_object_object_add(v18, "source_id", v28);
  v29 = json_object_new_string(v48);
  json_object_object_add(v18, "device_id", v29);
  qs_reply_json(a2, a4, v18, 0, 0LL);
  json_object_put(v18);
  auto_free_str((void **)&v33);
}
// 403F0C: variable 'v15' is possibly undefined
// 403F40: variable 'v17' is possibly undefined
// 402140: using guessed type __int64 __fastcall __strdup(_QWORD);
// 402190: using guessed type __int64 __fastcall json_object_new_string(_QWORD);
// 4021B0: using guessed type __int64 __fastcall qcloud_system_get_source_id(_QWORD);
// 4021D0: using guessed type __int64 __fastcall qcloud_get_device_info(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402380: using guessed type __int64 __fastcall json_object_get_string(_QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402440: using guessed type __int64 __fastcall qcloud_system_get_app_id(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402470: using guessed type __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD);
// 4024A0: using guessed type __int64 __fastcall qid_sys_get_qid_device_id(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402500: using guessed type __int64 __fastcall json_object_new_boolean(_QWORD);
// 4025B0: using guessed type __int64 __fastcall json_object_new_int(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);
// 402600: using guessed type __int64 __fastcall json_object_get_type(_QWORD);
// 403C10: using guessed type char var_BC0[2944];

//----- (0000000000404140) ----------------------------------------------------
__int64 __fastcall ncloud_system_add_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  __int64 v6; // rdx
  __int64 v7; // rcx
  char *v8; // r8
  const char *string; // rax
  const char *v11; // rax
  const char *v12; // rax
  const char *v13; // rax
  const char *v14; // rax
  const char *v15; // rax
  const char *v16; // rax
  const char *v17; // rax
  const char *v18; // rax
  const char *v19; // rax
  int v20; // eax
  __int64 v21; // rdx
  __int64 v22; // rcx
  __int64 v23; // rbx
  __int64 v24; // [rsp+8h] [rbp-620h] BYREF
  char v25[80]; // [rsp+10h] [rbp-618h] BYREF
  char dest[1432]; // [rsp+60h] [rbp-5C8h] BYREF
  int v27; // [rsp+5F8h] [rbp-30h]

  v24 = 0LL;
  memset(dest, 0, sizeof(dest));
  v27 = 0;
  json_object_object_get_ex(a3, "enabled", &v24);
  v8 = "enabled parameter incorrect";
  if ( !v24 )
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  *(_DWORD *)&dest[708] = json_object_get_boolean(v24, "enabled", v6, v7, "enabled parameter incorrect");
  json_object_object_get_ex(a3, "receiver_app_id", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_app_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  string = (const char *)json_object_get_string(v24);
  strncpy(&dest[463], string, 0x40uLL);
  json_object_object_get_ex(a3, "receiver_reg_id", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_reg_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v11 = (const char *)json_object_get_string(v24);
  strncpy(dest, v11, 0x12CuLL);
  json_object_object_get_ex(a3, "receiver_locale", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 )
  {
    v8 = "receiver_locale parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v12 = (const char *)json_object_get_string(v24);
  strncpy(&dest[859], v12, 8uLL);
  json_object_object_get_ex(a3, "receiver_user_id", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 )
  {
    v8 = "receiver_user_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v13 = (const char *)json_object_get_string(v24);
  strncpy(&dest[777], v13, 0x40uLL);
  json_object_object_get_ex(a3, "receiver_os_type", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_os_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v14 = (const char *)json_object_get_string(v24);
  strncpy(&dest[430], v14, 0x20uLL);
  json_object_object_get_ex(a3, "receiver_user_type", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 )
  {
    v8 = "receiver_user_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v15 = (const char *)json_object_get_string(v24);
  strncpy(&dest[842], v15, 0x10uLL);
  json_object_object_get_ex(a3, "receiver_os_version", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_os_version parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v16 = (const char *)json_object_get_string(v24);
  strncpy(&dest[674], v16, 0x20uLL);
  json_object_object_get_ex(a3, "receiver_app_version", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_app_version parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v17 = (const char *)json_object_get_string(v24);
  strncpy(&dest[528], v17, 0x10uLL);
  json_object_object_get_ex(a3, "receiver_device_name", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_device_name parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v18 = (const char *)json_object_get_string(v24);
  strncpy(&dest[301], v18, 0x80uLL);
  json_object_object_get_ex(a3, "receiver_device_type", &v24);
  if ( !v24 || (unsigned int)json_object_get_type(v24) != 6 || !(unsigned int)json_object_get_string_len(v24) )
  {
    v8 = "receiver_device_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v19 = (const char *)json_object_get_string(v24);
  strncpy(&dest[545], v19, 0x80uLL);
  memset(v25, 0, 0x41uLL);
  v20 = qcloud_system_add_receiver_device(dest, v25);
  v8 = "add receiver device fail";
  if ( v20 )
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  if ( (unsigned int)qcloud_system_get_receiver_device(v25, dest, v21, v22, "add receiver device fail") )
  {
    qcloud_system_delete_receiver_device(v25);
    v8 = "get receiver device fail";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v23 = sub_4039E0((__int64)dest);
  qs_reply_json(a2, a4, v23, 0, 0LL);
  return json_object_put(v23);
}
// 404199: variable 'v6' is possibly undefined
// 404199: variable 'v7' is possibly undefined
// 40456C: variable 'v21' is possibly undefined
// 40456C: variable 'v22' is possibly undefined
// 402160: using guessed type __int64 __fastcall json_object_get_boolean(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402290: using guessed type __int64 __fastcall qcloud_system_delete_receiver_device(_QWORD);
// 4022C0: using guessed type __int64 __fastcall qcloud_system_get_receiver_device(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402380: using guessed type __int64 __fastcall json_object_get_string(_QWORD);
// 402460: using guessed type __int64 __fastcall json_object_get_string_len(_QWORD);
// 402470: using guessed type __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD);
// 4024C0: using guessed type __int64 __fastcall qcloud_system_add_receiver_device(_QWORD, _QWORD);
// 402600: using guessed type __int64 __fastcall json_object_get_type(_QWORD);

//----- (00000000004045C0) ----------------------------------------------------
__int64 __fastcall ncloud_system_delete_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  char *v5; // r8
  const char *string; // rax
  __int64 v8; // rbx
  __int64 v9; // [rsp+8h] [rbp-70h] BYREF
  char dest[104]; // [rsp+10h] [rbp-68h] BYREF

  v9 = 0LL;
  memset(dest, 0, 65);
  json_object_object_get_ex(a3, "pair_id", &v9);
  if ( !v9 || (unsigned int)json_object_get_type(v9) != 6 || !(unsigned int)json_object_get_string_len(v9) )
  {
    v5 = "pair_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v5);
  }
  string = (const char *)json_object_get_string(v9);
  strncpy(dest, string, 0x40uLL);
  if ( (unsigned int)qcloud_system_delete_receiver_device(dest) )
  {
    v5 = "delete receiver device fail";
    return qs_reply_json(a2, a4, 0LL, 1u, v5);
  }
  v8 = json_object_new_object(dest);
  qs_reply_json(a2, a4, v8, 0, 0LL);
  return json_object_put(v8);
}
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402290: using guessed type __int64 __fastcall qcloud_system_delete_receiver_device(_QWORD);
// 402380: using guessed type __int64 __fastcall json_object_get_string(_QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402460: using guessed type __int64 __fastcall json_object_get_string_len(_QWORD);
// 402470: using guessed type __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD);
// 402600: using guessed type __int64 __fastcall json_object_get_type(_QWORD);

//----- (00000000004046B0) ----------------------------------------------------
__int64 __fastcall ncloud_system_update_receiver_device(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  __int64 v6; // rdx
  __int64 v7; // rcx
  char *v8; // r8
  const char *string; // rax
  const char *v11; // rax
  const char *v12; // rax
  const char *v13; // rax
  const char *v14; // rax
  const char *v15; // rax
  const char *v16; // rax
  const char *v17; // rax
  const char *v18; // rax
  const char *v19; // rax
  const char *v20; // rax
  int updated; // eax
  __int64 v22; // rdx
  __int64 v23; // rcx
  __int64 v24; // rbx
  __int64 v25; // [rsp+8h] [rbp-5D0h] BYREF
  char dest[1432]; // [rsp+10h] [rbp-5C8h] BYREF
  int v27; // [rsp+5A8h] [rbp-30h]

  v25 = 0LL;
  memset(dest, 0, sizeof(dest));
  v27 = 0;
  json_object_object_get_ex(a3, "enabled", &v25);
  v8 = "enabled parameter incorrect";
  if ( !v25 )
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  *(_DWORD *)&dest[708] = json_object_get_boolean(v25, "enabled", v6, v7, "enabled parameter incorrect");
  json_object_object_get_ex(a3, "receiver_app_id", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_app_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  string = (const char *)json_object_get_string(v25);
  strncpy(&dest[463], string, 0x40uLL);
  json_object_object_get_ex(a3, "receiver_reg_id", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_reg_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v11 = (const char *)json_object_get_string(v25);
  strncpy(dest, v11, 0x12CuLL);
  json_object_object_get_ex(a3, "receiver_locale", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 )
  {
    v8 = "receiver_locale parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v12 = (const char *)json_object_get_string(v25);
  strncpy(&dest[859], v12, 8uLL);
  json_object_object_get_ex(a3, "receiver_user_id", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 )
  {
    v8 = "receiver_user_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v13 = (const char *)json_object_get_string(v25);
  strncpy(&dest[777], v13, 0x40uLL);
  json_object_object_get_ex(a3, "receiver_os_type", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_os_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v14 = (const char *)json_object_get_string(v25);
  strncpy(&dest[430], v14, 0x20uLL);
  json_object_object_get_ex(a3, "receiver_user_type", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 )
  {
    v8 = "receiver_user_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v15 = (const char *)json_object_get_string(v25);
  strncpy(&dest[842], v15, 0x10uLL);
  json_object_object_get_ex(a3, "receiver_os_version", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_os_version parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v16 = (const char *)json_object_get_string(v25);
  strncpy(&dest[674], v16, 0x20uLL);
  json_object_object_get_ex(a3, "receiver_app_version", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_app_version parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v17 = (const char *)json_object_get_string(v25);
  strncpy(&dest[528], v17, 0x10uLL);
  json_object_object_get_ex(a3, "receiver_device_name", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_device_name parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v18 = (const char *)json_object_get_string(v25);
  strncpy(&dest[301], v18, 0x80uLL);
  json_object_object_get_ex(a3, "receiver_device_type", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "receiver_device_type parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v19 = (const char *)json_object_get_string(v25);
  strncpy(&dest[545], v19, 0x80uLL);
  json_object_object_get_ex(a3, "pair_id", &v25);
  if ( !v25 || (unsigned int)json_object_get_type(v25) != 6 || !(unsigned int)json_object_get_string_len(v25) )
  {
    v8 = "pair_id parameter incorrect";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v20 = (const char *)json_object_get_string(v25);
  strncpy(&dest[892], v20, 0x40uLL);
  updated = qcloud_system_update_receiver_device(&dest[892], dest);
  v8 = "update receiver device fail";
  if ( updated )
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  if ( (unsigned int)qcloud_system_get_receiver_device(&dest[892], dest, v22, v23, "update receiver device fail") )
  {
    v8 = "get receiver device fail";
    return qs_reply_json(a2, a4, 0LL, 1u, v8);
  }
  v24 = sub_4039E0((__int64)dest);
  qs_reply_json(a2, a4, v24, 0, 0LL);
  return json_object_put(v24);
}
// 404709: variable 'v6' is possibly undefined
// 404709: variable 'v7' is possibly undefined
// 404B2A: variable 'v22' is possibly undefined
// 404B2A: variable 'v23' is possibly undefined
// 402160: using guessed type __int64 __fastcall json_object_get_boolean(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402240: using guessed type __int64 __fastcall qcloud_system_update_receiver_device(_QWORD, _QWORD);
// 4022C0: using guessed type __int64 __fastcall qcloud_system_get_receiver_device(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 402380: using guessed type __int64 __fastcall json_object_get_string(_QWORD);
// 402460: using guessed type __int64 __fastcall json_object_get_string_len(_QWORD);
// 402470: using guessed type __int64 __fastcall json_object_object_get_ex(_QWORD, _QWORD, _QWORD);
// 402600: using guessed type __int64 __fastcall json_object_get_type(_QWORD);

//----- (0000000000404B70) ----------------------------------------------------
__int64 __fastcall ncloud_system_get_receiver_device_list(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  int receiver_device_list; // eax
  __int64 v7; // r13
  unsigned __int64 v8; // r14
  char *v9; // rbx
  __int64 v10; // rdi
  __int64 v11; // r12
  __int64 v12; // rbp
  __int64 v13; // rax
  __int64 v14; // rax
  __int64 v15; // rax
  __int64 v16; // rax
  __int64 v17; // rax
  __int64 v18; // rax
  __int64 v19; // rax
  __int64 v20; // rax
  __int64 v21; // rax
  __int64 v22; // rax
  __int64 v23; // rax
  __int64 v24; // rax
  __int64 v25; // rax
  __int64 v26; // rax
  __int64 v27; // rax
  __int64 v28; // rax
  __int64 v29; // rax
  __int64 v30; // rax
  char *v31; // rdi
  __int64 v32; // rax
  char s[367616]; // [rsp+10h] [rbp-59C48h] BYREF
  unsigned __int64 v34; // [rsp+59C10h] [rbp-48h]

  memset(s, 0, 0x59C08uLL);
  receiver_device_list = qcloud_system_get_receiver_device_list(0LL, 200LL, s);
  if ( (unsigned int)(receiver_device_list + 3002) > 1 && receiver_device_list )
    return qs_reply_json(a2, a4, 0LL, 1u, "get receiver device list fail");
  v7 = json_object_new_object(0LL);
  v8 = 0LL;
  v9 = s;
  v10 = v7;
  v11 = json_object_new_array();
  json_object_object_add(v7, "data", v11);
  if ( v34 )
  {
    do
    {
      v12 = json_object_new_object(v10);
      v13 = json_object_new_boolean(*((_DWORD *)v9 + 177) == 1);
      json_object_object_add(v12, "enabled", v13);
      v14 = json_object_new_string(v9 + 868);
      json_object_object_add(v12, "receiver_type", v14);
      v15 = json_object_new_string(v9 + 463);
      json_object_object_add(v12, "receiver_app_id", v15);
      v16 = json_object_new_string(v9);
      json_object_object_add(v12, "receiver_reg_id", v16);
      v17 = json_object_new_string(v9 + 859);
      json_object_object_add(v12, "receiver_locale", v17);
      v18 = json_object_new_string(v9 + 777);
      json_object_object_add(v12, "receiver_user_id", v18);
      v19 = json_object_new_string(v9 + 430);
      json_object_object_add(v12, "receiver_os_type", v19);
      v20 = json_object_new_string(v9 + 842);
      json_object_object_add(v12, "receiver_user_type", v20);
      v21 = json_object_new_string(v9 + 674);
      json_object_object_add(v12, "receiver_os_version", v21);
      v22 = json_object_new_string(v9 + 528);
      json_object_object_add(v12, "receiver_app_version", v22);
      v23 = json_object_new_string(v9 + 301);
      json_object_object_add(v12, "receiver_device_name", v23);
      v24 = json_object_new_string(v9 + 545);
      json_object_object_add(v12, "receiver_device_type", v24);
      v25 = json_object_new_string(v9 + 892);
      json_object_object_add(v12, "pair_id", v25);
      v26 = json_object_new_string(v9 + 1345);
      json_object_object_add(v12, 4215295LL, v26);
      v27 = json_object_new_string(v9 + 1215);
      json_object_object_add(v12, "created_at", v27);
      v28 = json_object_new_string(v9 + 1280);
      json_object_object_add(v12, "updated_at", v28);
      v29 = json_object_new_int(*((unsigned int *)v9 + 353));
      json_object_object_add(v12, "reg_id_status", v29);
      ++v8;
      v30 = json_object_new_string(v9 + 957);
      json_object_object_add(v12, "receiver_app_name", v30);
      v31 = v9 + 1086;
      v9 += 1436;
      v32 = json_object_new_string(v31);
      json_object_object_add(v12, "receiver_app_display_name", v32);
      v10 = v11;
      json_object_array_add(v11, v12);
    }
    while ( v34 > v8 );
  }
  qs_reply_json(a2, a4, v7, 0, 0LL);
  return json_object_put(v7);
}
// 402190: using guessed type __int64 __fastcall json_object_new_string(_QWORD);
// 4021C0: using guessed type __int64 __fastcall qcloud_system_get_receiver_device_list(_QWORD, _QWORD, _QWORD);
// 4021F0: using guessed type __int64 json_object_new_array(void);
// 402220: using guessed type __int64 __fastcall json_object_put(_QWORD);
// 402350: using guessed type __int64 __fastcall json_object_array_add(_QWORD, _QWORD);
// 4023A0: using guessed type __int64 __fastcall json_object_new_object(_QWORD);
// 402500: using guessed type __int64 __fastcall json_object_new_boolean(_QWORD);
// 4025B0: using guessed type __int64 __fastcall json_object_new_int(_QWORD);
// 4025F0: using guessed type __int64 __fastcall json_object_object_add(_QWORD, _QWORD, _QWORD);

//----- (0000000000404E90) ----------------------------------------------------
void _libc_csu_init(void)
{
  init_proc();
}

//----- (0000000000404F10) ----------------------------------------------------
void (*sub_404F10())(void)
{
  void (**v0)(void); // rbx
  void (*result)(void); // rax

  v0 = (void (**)(void))&qword_606080;
  result = (void (*)(void))qword_606080;
  if ( qword_606080 != -1 )
  {
    do
    {
      --v0;
      result();
      result = *v0;
    }
    while ( *v0 != (void (*)(void))-1LL );
  }
  return result;
}
// 606080: using guessed type __int64 qword_606080;

//----- (0000000000404F48) ----------------------------------------------------
void term_proc()
{
  sub_402940();
}

// nfuncs=188 queued=31 decompiled=31 lumina nreq=0 worse=0 better=0
// ALL OK, 31 function(s) have been successfully decompiled
