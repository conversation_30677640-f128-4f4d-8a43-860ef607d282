/* This file was generated by the Hex-Rays decompiler version 7.7.0.220118.
   Copyright (c) 2007-2021 Hex-Rays <<EMAIL>>

   Detected compiler: GNU C++
*/

#include <defs.h>


//-------------------------------------------------------------------------
// Function declarations

void (*init_proc())(void);
__int64 __fastcall sub_401340(); // weak
// int printf(const char *format, ...);
// __int64 __fastcall json_string(_QWORD); weak
// __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// char *strstr(const char *haystack, const char *needle);
// __pid_t fork(void);
// __int64 qs_io_create(void); weak
// __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// char *strchr(const char *s, int c);
// __int64 __fastcall json_dumpf(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD); weak
// size_t strlen(const char *s);
// int strncmp(const char *s1, const char *s2, size_t n);
// __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD); weak
// __int64 sysconf(int name);
// __int64 __fastcall qs_io_dup2(_QWORD, _QWORD); weak
// __pid_t waitpid(__pid_t pid, int *stat_loc, int options);
// __int64 json_object(void); weak
// __int64 __fastcall json_integer(_QWORD); weak
// int dup2(int fd, int fd2);
// const unsigned __int16 **__ctype_b_loc(void);
// int strcasecmp(const char *s1, const char *s2);
// __int64 __fastcall qs_io_read(_QWORD, _QWORD); weak
// void __noreturn exit(int status);
// char *getenv(const char *name);
// __int64 __fastcall User_Belongs_To_Group(_QWORD, _QWORD); weak
// __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD); weak
// __int64 json_array(void); weak
// int strcmp(const char *s1, const char *s2);
// __int64 __fastcall json_delete(_QWORD); weak
// int __xstat64(int ver, const char *filename, struct stat64 *stat_buf);
// __int64 __fastcall qs_io_free(_QWORD); weak
// __int64 __fastcall json_loadf(_QWORD, _QWORD, _QWORD); weak
// char *strtok(char *s, const char *delim);
// int snprintf(char *s, size_t maxlen, const char *format, ...);
// void __noreturn _exit(int status);
// int execve(const char *path, char *const argv[], char *const envp[]);
// int open64(const char *file, int oflag, ...);
// __int64 __fastcall auth_get_session(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_realloc(_QWORD, _QWORD); weak
// int __fastcall __libc_start_main(int (__fastcall *main)(int, char **, char **), int argc, char **ubp_av, void (*init)(void), void (*fini)(void), void (*rtld_fini)(void), void *stack_end);
// __int64 __gmon_start__(void); weak
// int close(int fd);
// __int64 __fastcall json_object_get(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_size(_QWORD); weak
// __int64 __fastcall json_array_append_new(_QWORD, _QWORD); weak
int __cdecl main(int argc, const char **argv, const char **envp);
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void));
signed __int64 sub_401B60();
void sub_401BE0();
__int64 sub_401C50();
void __fastcall sub_401C80(__int64 a1);
void __fastcall __noreturn sub_401CB0(unsigned int a1, __int64 a2);
void __fastcall __noreturn sub_401D20(__int64 a1, __int64 a2);
__int64 __fastcall sub_401E80(__int64 a1, __int64 a2, __int64 a3);
__int64 sub_401EA0();
void _libc_csu_init(void); // idb
void _libc_csu_fini(void); // idb
void (*sub_4021F0())(void);
void term_proc();

//-------------------------------------------------------------------------
// Data declarations

__int64 qword_6026A8 = -1LL; // weak
__int64 qword_6026B8[] = { -1LL }; // weak
__int64 qword_6026C0 = 0LL; // weak
__int64 (*qword_6028E8)(void) = NULL; // weak
_UNKNOWN _bss_start; // weak
_UNKNOWN unk_602A6F; // weak
__int64 stdout; // weak
char **environ;
__int64 stdin; // weak
char byte_602A98; // weak
__int64 qword_602AA0; // weak
char byte_602AC0; // weak
_UNKNOWN unk_602B00; // weak
_UNKNOWN unk_602B52; // weak
char s[256]; // idb
// extern _UNKNOWN _gmon_start__; weak


//----- (0000000000401318) ----------------------------------------------------
void (*init_proc())(void)
{
  if ( &_gmon_start__ )
    __gmon_start__();
  sub_401C50();
  return sub_4021F0();
}
// 4015D0: using guessed type __int64 __gmon_start__(void);

//----- (0000000000401340) ----------------------------------------------------
__int64 sub_401340()
{
  return qword_6028E8();
}
// 401340: using guessed type __int64 __fastcall sub_401340();
// 6028E8: using guessed type __int64 (*qword_6028E8)(void);

//----- (0000000000401620) ----------------------------------------------------
int __cdecl __noreturn main(int argc, const char **argv, const char **envp)
{
  char *v3; // rbx
  const char *v4; // rdi
  char *v5; // rax
  const char *v6; // rbx
  char *v7; // rax
  struct stat64 *p_stat_buf; // rdi
  __int64 i; // rcx
  __int64 v10; // rbx
  char **v11; // r12
  __int64 v12; // rax
  int v13; // eax
  int v14; // ebp
  const char *v15; // rax
  char *v16; // rbp
  char *v17; // rbx
  char v18; // si
  const char *v19; // r12
  char *v20; // rdx
  __int64 v21; // rax
  const char *v22; // rcx
  char *v23; // rax
  unsigned int v24; // eax
  __int64 v25; // rax
  char *v26; // rbx
  char *v27; // rax
  const char *v28; // rbx
  char *v29; // rax
  int v30; // eax
  int v31; // edi
  int v32; // r13d
  int v33; // edi
  __int64 v34; // rbx
  __int64 v35; // rbp
  int v36; // r13d
  int v37; // edi
  int stat_loc; // [rsp+Ch] [rbp-FCh] BYREF
  char *argva[8]; // [rsp+10h] [rbp-F8h] BYREF
  struct stat64 stat_buf; // [rsp+50h] [rbp-B8h] BYREF

  v3 = getenv("QUERY_STRING");
  if ( !v3 )
    v3 = (char *)"";
  v4 = v3;
  while ( 1 )
  {
    v5 = strstr(v4, "sid=");
    v4 = v5;
    if ( !v5 )
      break;
    if ( v3 >= v5 || *(v5 - 1) == 38 )
    {
      v6 = strchr(v5, 61) + 1;
      v7 = strchr(v6, 38);
      if ( v7 )
      {
        snprintf(s, 0x100uLL, "%.*s", (_DWORD)v7 - (_DWORD)v6, v6);
LABEL_9:
        if ( (unsigned int)auth_get_session(s, 1LL, &unk_602B00)
          || !(unsigned int)User_Belongs_To_Group(&unk_602B52, "administrators") )
        {
          if ( !(unsigned int)auth_get_session(s, 0LL, &unk_602B00) )
          {
            p_stat_buf = &stat_buf;
            for ( i = 36LL; i; --i )
            {
              LODWORD(p_stat_buf->st_dev) = 0;
              p_stat_buf = (struct stat64 *)((char *)p_stat_buf + 4);
            }
            if ( !__xstat64(1, "/sbin/role_delegation", &stat_buf) )
            {
              v10 = qs_io_create();
              qs_io_realloc(v10, 0LL);
              argva[0] = "/sbin/role_delegation";
              argva[1] = "--is_delegated";
              argva[2] = "-r";
              argva[3] = "1";
              argva[4] = "-u";
              argva[5] = (char *)&unk_602B52;
              argva[6] = 0LL;
              v11 = environ;
              if ( v10 )
              {
                v12 = qs_io_size(v10);
                qs_io_realloc(v10, v12);
              }
              v13 = fork();
              v14 = v13;
              if ( v13 >= 0 )
              {
                if ( !v13 )
                {
                  v30 = open64("/dev/null", 2);
                  v31 = v14;
                  v32 = v30;
                  if ( v30 >= 0 )
                    v31 = v30;
                  dup2(v31, 0);
                  if ( v10 )
                  {
                    qs_io_dup2(v10, 1LL);
                  }
                  else
                  {
                    v37 = 1;
                    if ( v32 >= 0 )
                      v37 = v32;
                    dup2(v37, 1);
                  }
                  v33 = 2;
                  if ( v32 >= 0 )
                    v33 = v32;
                  v34 = 3LL;
                  dup2(v33, 2);
                  while ( 1 )
                  {
                    v35 = v34;
                    v36 = v34++;
                    if ( v35 >= sysconf(4) )
                      break;
                    close(v36);
                  }
                  execve("/sbin/role_delegation", argva, v11);
                  _exit(1);
                }
                stat_loc = -1;
                waitpid(v13, &stat_loc, 0);
              }
              if ( qs_io_size(v10) )
              {
                v15 = (const char *)qs_io_read(v10, 0LL);
                if ( !strncmp(v15, "delegated", 9uLL) )
                {
                  byte_602AC0 = 1;
                  if ( v10 )
                    qs_io_free(v10);
                  goto LABEL_28;
                }
              }
              byte_602AC0 = 0;
              if ( v10 )
                qs_io_free(v10);
            }
          }
LABEL_12:
          sub_401CB0(0x191u, 0LL);
        }
LABEL_28:
        v16 = getenv("SCRIPT_URL");
        v17 = getenv("REQUEST_METHOD");
        if ( !v17 || !v16 || strncmp(v16, "/nc/api/", 8uLL) )
          goto LABEL_41;
        v18 = v16[8];
        v19 = v16 + 8;
        v20 = v16 + 8;
        if ( v18 )
        {
          do
          {
            while ( v18 != 47 )
            {
              v18 = *++v20;
              if ( !*v20 )
                goto LABEL_36;
            }
            *v20++ = 95;
            v18 = *v20;
          }
          while ( *v20 );
        }
LABEL_36:
        memset(&stat_buf, 0, 0x41uLL);
        if ( !strcasecmp(v17, "GET") )
        {
          snprintf((char *)&stat_buf, 0x40uLL, "get_%s", v19);
        }
        else if ( !strcasecmp(v17, "PUT") )
        {
          snprintf((char *)&stat_buf, 0x40uLL, "set_%s", v19);
        }
        else
        {
          if ( strcasecmp(v17, "POST") )
          {
            if ( !strcasecmp(v17, "DELETE") )
            {
              snprintf((char *)&stat_buf, 0x40uLL, "del_%s", v19);
              goto LABEL_43;
            }
LABEL_41:
            sub_401CB0(0x190u, 0LL);
          }
          snprintf((char *)&stat_buf, 0x40uLL, "add_%s", v19);
        }
LABEL_43:
        if ( strcmp((const char *)&stat_buf, "get_session") )
        {
          if ( !strcasecmp(v17, "GET") || (v21 = json_loadf(stdin, 0LL, 0LL)) == 0 )
            v21 = sub_401EA0();
          sub_401D20((__int64)&stat_buf, v21);
        }
        v24 = User_Belongs_To_Group(&unk_602B52, "administrators");
        v25 = json_pack("{s{ss sb}}", "data", "username", &unk_602B52, "is_admin", v24);
        sub_401CB0(0xC8u, v25);
      }
      v22 = v6;
LABEL_48:
      snprintf(s, 0x100uLL, "%s", v22);
      goto LABEL_9;
    }
  }
  v23 = getenv("HTTP_X_NC_SESSION");
  if ( !v23 )
  {
    v26 = getenv("HTTP_COOKIE");
    if ( !v26 )
      goto LABEL_12;
    if ( !getenv("REQUEST_SCHEME") )
      goto LABEL_12;
    v27 = strstr(v26, "NAS_SID=");
    v28 = v27;
    if ( !v27 )
      goto LABEL_12;
    v29 = strchr(v27, 59);
    if ( v29 )
      *v29 = 0;
    v23 = strchr(v28, 61) + 1;
  }
  v22 = v23;
  goto LABEL_48;
}
// 4013A0: using guessed type __int64 qs_io_create(void);
// 401430: using guessed type __int64 __fastcall qs_io_dup2(_QWORD, _QWORD);
// 4014A0: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 4014D0: using guessed type __int64 __fastcall User_Belongs_To_Group(_QWORD, _QWORD);
// 401530: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 401540: using guessed type __int64 __fastcall json_loadf(_QWORD, _QWORD, _QWORD);
// 4015A0: using guessed type __int64 __fastcall auth_get_session(_QWORD, _QWORD, _QWORD);
// 4015B0: using guessed type __int64 __fastcall qs_io_realloc(_QWORD, _QWORD);
// 401600: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 602A90: using guessed type __int64 stdin;
// 602AC0: using guessed type char byte_602AC0;

//----- (0000000000401B30) ----------------------------------------------------
// positive sp value has been detected, the output may be wrong!
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void))
{
  __int64 v3; // rax
  int v4; // esi
  __int64 v5; // [rsp-8h] [rbp-8h] BYREF
  char *retaddr; // [rsp+0h] [rbp+0h] BYREF

  v4 = v5;
  v5 = v3;
  __libc_start_main(
    (int (__fastcall *)(int, char **, char **))main,
    v4,
    &retaddr,
    _libc_csu_init,
    _libc_csu_fini,
    a3,
    &v5);
  __halt();
}
// 401B36: positive sp value 8 has been found
// 401B3D: variable 'v3' is possibly undefined

//----- (0000000000401B60) ----------------------------------------------------
signed __int64 sub_401B60()
{
  signed __int64 result; // rax

  result = &unk_602A6F - &_bss_start;
  if ( (unsigned __int64)(&unk_602A6F - &_bss_start) > 0xE )
    return 0LL;
  return result;
}

//----- (0000000000401BE0) ----------------------------------------------------
void sub_401BE0()
{
  __int64 v0; // rax
  unsigned __int64 i; // rbx

  if ( !byte_602A98 )
  {
    v0 = qword_602AA0;
    for ( i = &qword_6026C0 - qword_6026B8 - 1; qword_602AA0 < i; v0 = qword_602AA0 )
    {
      qword_602AA0 = v0 + 1;
      ((void (*)(void))qword_6026B8[v0 + 1])();
    }
    sub_401B60();
    byte_602A98 = 1;
  }
}
// 6026B8: using guessed type __int64 qword_6026B8[];
// 6026C0: using guessed type __int64 qword_6026C0;
// 602A98: using guessed type char byte_602A98;
// 602AA0: using guessed type __int64 qword_602AA0;

//----- (0000000000401C50) ----------------------------------------------------
__int64 sub_401C50()
{
  return 0LL;
}

//----- (0000000000401C80) ----------------------------------------------------
void __fastcall sub_401C80(__int64 a1)
{
  __int64 v1; // rax
  __int64 v2; // rax

  if ( a1 )
  {
    v1 = *(_QWORD *)(a1 + 8);
    if ( v1 != -1 )
    {
      v2 = v1 - 1;
      *(_QWORD *)(a1 + 8) = v2;
      if ( !v2 )
        json_delete(a1);
    }
  }
}
// 401510: using guessed type __int64 __fastcall json_delete(_QWORD);

//----- (0000000000401CB0) ----------------------------------------------------
void __fastcall __noreturn sub_401CB0(unsigned int a1, __int64 a2)
{
  __int64 v2; // rbx
  __int64 v3; // rax

  v2 = a2;
  printf("Status: %d\r\nContent-Type: application/json; charset=utf-8\r\n\r\n", a1);
  if ( !a2 )
    v2 = json_object();
  v3 = json_integer((int)a1);
  json_object_set_new(v2, "status", v3);
  json_dumpf(v2, stdout, 5152LL);
  sub_401C80(v2);
  exit(0);
}
// 4013D0: using guessed type __int64 __fastcall json_dumpf(_QWORD, _QWORD, _QWORD);
// 401450: using guessed type __int64 json_object(void);
// 401460: using guessed type __int64 __fastcall json_integer(_QWORD);
// 4014E0: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 602A80: using guessed type __int64 stdout;

//----- (0000000000401D20) ----------------------------------------------------
void __fastcall __noreturn sub_401D20(__int64 a1, __int64 a2)
{
  char *v2; // rax
  __int64 v3; // rax
  __int64 v4; // rbp
  __int64 v5; // rax
  char *v6; // rax
  __int64 v7; // rax

  getenv("REQUEST_SCHEME");
  getenv("REMOTE_ADDR");
  getenv("SERVER_PORT");
  v2 = getenv("SERVER_NAME");
  v3 = json_pack("{ss ss ss ss ss ss}", "sid", s, "host", v2, "port");
  json_object_set_new(a2, "__ENV__", v3);
  v4 = qs_io_create();
  v5 = json_string(&unk_602B52);
  json_object_set_new(a2, "__USER__", v5);
  v6 = getenv("REMOTE_ADDR");
  v7 = json_string(v6);
  json_object_set_new(a2, "__REMOTE__", v7);
  json_dump_callback(a2, sub_401E80, v4, 5152LL);
  sub_401C80(a2);
  switch ( (unsigned int)qs_call("nc", a1, v4, 180LL, 1LL) )
  {
    case 1u:
      sub_401CB0(0x198u, 0LL);
    case 2u:
    case 3u:
    case 6u:
      sub_401CB0(0x1F7u, 0LL);
    case 4u:
    case 5u:
      sub_401CB0(0x193u, 0LL);
    default:
      qs_io_free(v4);
      exit(0);
  }
}
// 401360: using guessed type __int64 __fastcall json_string(_QWORD);
// 401370: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 4013A0: using guessed type __int64 qs_io_create(void);
// 4013E0: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 4014E0: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 401530: using guessed type __int64 __fastcall qs_io_free(_QWORD);

//----- (0000000000401E80) ----------------------------------------------------
__int64 __fastcall sub_401E80(__int64 a1, __int64 a2, __int64 a3)
{
  return (unsigned int)qs_io_write(a3, a1, a2) - (unsigned int)a2;
}
// 401410: using guessed type __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD);

//----- (0000000000401EA0) ----------------------------------------------------
__int64 sub_401EA0()
{
  char *v0; // rax
  char *v1; // r13
  __int64 v2; // rdx
  const char *v3; // rbx
  size_t v4; // r15
  __int64 v5; // r12
  size_t v6; // rbp
  char v7; // dl
  _DWORD *v8; // rax
  _DWORD *v9; // r12
  __int64 v10; // rbp
  __int64 v11; // rax
  __int64 v12; // rax
  __int64 v13; // rdx
  __int64 v14; // rax
  __int64 v15; // rax
  __int64 v16; // rax
  const unsigned __int16 *v18; // rsi
  size_t v19; // rax
  char v20; // cl
  char v21; // dl
  char v22; // al
  char v23; // cl
  char v24; // al
  __int64 v25; // rax
  __int64 v26; // rax
  __int64 v27; // [rsp+8h] [rbp-40h]

  v27 = json_object();
  v0 = getenv("QUERY_STRING");
  if ( !v0 )
    v0 = (char *)"";
  v1 = strtok(v0, "&");
  if ( v1 )
  {
    while ( 1 )
    {
      v2 = (unsigned __int8)*v1;
      v3 = v1;
      if ( !(_BYTE)v2 )
      {
LABEL_10:
        v4 = strlen(v3);
        if ( v4 )
          break;
        goto LABEL_46;
      }
      while ( 1 )
      {
        while ( (_BYTE)v2 == 91 )
        {
          *v3++ = 0;
          v2 = *(unsigned __int8 *)v3;
          if ( !(_BYTE)v2 )
            goto LABEL_10;
        }
        if ( (_BYTE)v2 == 61 )
          break;
        v2 = *(unsigned __int8 *)++v3;
        if ( !(_BYTE)v2 )
          goto LABEL_10;
      }
      *v3++ = 0;
      if ( !v3 )
        goto LABEL_19;
      v4 = strlen(v3);
      if ( v4 )
        break;
LABEL_46:
      v5 = 0LL;
LABEL_18:
      v3[v5] = 0;
LABEL_19:
      v8 = (_DWORD *)json_object_get(v27, v1, v2);
      v9 = v8;
      if ( v8 )
      {
        if ( *v8 == 1 )
        {
          v25 = json_string(v3);
          json_array_append_new(v9, v25);
        }
        else
        {
          v10 = json_array();
          v11 = *((_QWORD *)v9 + 1);
          if ( v11 != -1 )
            *((_QWORD *)v9 + 1) = v11 + 1;
          json_array_append_new(v10, v9);
          v12 = json_string(v3);
          if ( v12 )
          {
            v13 = *(_QWORD *)(v12 + 8);
            if ( v13 != -1 )
              *(_QWORD *)(v12 + 8) = v13 + 1;
          }
          json_array_append_new(v10, v12);
          if ( v10 )
          {
            v14 = *(_QWORD *)(v10 + 8);
            if ( v14 != -1 )
              *(_QWORD *)(v10 + 8) = v14 + 1;
            json_object_set_new(v27, v1, v10);
            v15 = *(_QWORD *)(v10 + 8);
            if ( v15 != -1 )
            {
              v16 = v15 - 1;
              *(_QWORD *)(v10 + 8) = v16;
              if ( !v16 )
                json_delete(v10);
            }
          }
          else
          {
            json_object_set_new(v27, v1, 0LL);
          }
        }
      }
      else
      {
        v26 = json_string(v3);
        json_object_set_new(v27, v1, v26);
      }
      v1 = strtok(0LL, "&");
      if ( !v1 )
        return v27;
    }
    v5 = 1LL;
    v6 = 0LL;
    while ( 1 )
    {
      v7 = v3[v6];
      if ( v7 == 37 )
        break;
      if ( v7 == 43 )
      {
        v3[v5 - 1] = 32;
        ++v6;
        goto LABEL_13;
      }
      ++v6;
      v3[v5 - 1] = v7;
      v2 = v5 + 1;
      if ( v4 <= v6 )
        goto LABEL_18;
LABEL_14:
      v5 = v2;
    }
    if ( v4 <= v6 + 2 )
    {
      v19 = v6 + 1;
    }
    else
    {
      v18 = *__ctype_b_loc();
      v19 = v6 + 1;
      v20 = v3[v6 + 1];
      if ( (v18[v20] & 0x1000) != 0 )
      {
        v21 = v3[v6 + 2];
        if ( (v18[v21] & 0x1000) != 0 )
        {
          v22 = v20 - 48;
          if ( v20 > 64 )
            v22 = (v20 & 0xDF) - 55;
          v23 = 16 * v22;
          v24 = v21 - 48;
          if ( v21 > 64 )
            v24 = (v21 & 0xDF) - 55;
          v6 += 3LL;
          v3[v5 - 1] = v24 + v23;
LABEL_13:
          v2 = v5 + 1;
          if ( v4 <= v6 )
            goto LABEL_18;
          goto LABEL_14;
        }
      }
    }
    v3[v5 - 1] = 37;
    v6 = v19;
    goto LABEL_13;
  }
  return v27;
}
// 401F91: variable 'v2' is possibly undefined
// 401360: using guessed type __int64 __fastcall json_string(_QWORD);
// 401450: using guessed type __int64 json_object(void);
// 4014E0: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 4014F0: using guessed type __int64 json_array(void);
// 401510: using guessed type __int64 __fastcall json_delete(_QWORD);
// 4015F0: using guessed type __int64 __fastcall json_object_get(_QWORD, _QWORD, _QWORD);
// 401610: using guessed type __int64 __fastcall json_array_append_new(_QWORD, _QWORD);

//----- (0000000000402170) ----------------------------------------------------
void _libc_csu_init(void)
{
  init_proc();
}

//----- (00000000004021F0) ----------------------------------------------------
void (*sub_4021F0())(void)
{
  void (**v0)(void); // rbx
  void (*result)(void); // rax

  v0 = (void (**)(void))&qword_6026A8;
  result = (void (*)(void))qword_6026A8;
  if ( qword_6026A8 != -1 )
  {
    do
    {
      --v0;
      result();
      result = *v0;
    }
    while ( *v0 != (void (*)(void))-1LL );
  }
  return result;
}
// 6026A8: using guessed type __int64 qword_6026A8;

//----- (0000000000402228) ----------------------------------------------------
void term_proc()
{
  sub_401BE0();
}

// nfuncs=106 queued=15 decompiled=15 lumina nreq=0 worse=0 better=0
// ALL OK, 15 function(s) have been successfully decompiled
