/* This file was generated by the Hex-Rays decompiler version 7.7.0.220118.
   Copyright (c) 2007-2021 Hex-<PERSON>s <<EMAIL>>

   Detected compiler: GNU C++
*/

#include <defs.h>


//-------------------------------------------------------------------------
// Function declarations

void (*init_proc())(void);
__int64 __fastcall sub_4017F0(); // weak
// void free(void *ptr);
// __int64 __fastcall json_object_iter_value(_QWORD); weak
// __int64 __fastcall json_array(_QWORD); weak
// __int64 __fastcall json_array_append_new(_QWORD, _QWORD); weak
// __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_integer(_QWORD); weak
// int setenv(const char *name, const char *value, int replace);
// char *textdomain(const char *domainname);
// __int64 json_object(void); weak
// __int64 __fastcall json_dumpf(_QWORD, _QWORD, _QWORD); weak
// char *bindtextdomain(const char *domainname, const char *dirname);
// __int64 __fastcall json_delete(_QWORD); weak
// char *dcgettext(const char *domainname, const char *msgid, int category);
// void regfree(regex_t *preg);
// size_t strlen(const char *s);
// int getopt_long(int argc, char *const *argv, const char *shortopts, const struct option *longopts, int *longind);
// __int64 __fastcall qs_io_create(_QWORD); weak
// int printf(const char *format, ...);
// __int64 __fastcall json_object_iter_next(_QWORD, _QWORD); weak
// int snprintf(char *s, size_t maxlen, const char *format, ...);
// __int64 __fastcall json_array_get(_QWORD, _QWORD); weak
// __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD); weak
// int regcomp(regex_t *preg, const char *pattern, int cflags);
// int fputc(int c, FILE *stream);
// __int64 __fastcall __strdup(_QWORD); weak
// __int64 json_true(void); weak
// int __fastcall __libc_start_main(int (__fastcall *main)(int, char **, char **), int argc, char **ubp_av, void (*init)(void), void (*fini)(void), void (*rtld_fini)(void), void *stack_end);
// int strcmp(const char *s1, const char *s2);
// __int64 strtoll(const char *nptr, char **endptr, int base);
// __int64 __fastcall __strndup(_QWORD, _QWORD); weak
// int fprintf(FILE *stream, const char *format, ...);
// char *basename(const char *filename);
// __int64 __gmon_start__(void); weak
// __int64 strtol(const char *nptr, char **endptr, int base);
// __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_string(_QWORD); weak
// char *realpath(const char *name, char *resolved);
// int regexec(const regex_t *preg, const char *string, size_t nmatch, regmatch_t pmatch[], int eflags);
// __int64 __fastcall qs_io_free(_QWORD); weak
// __int64 __fastcall json_array_size(_QWORD); weak
// char *setlocale(int category, const char *locale);
// __int64 __fastcall qs_io_size(_QWORD); weak
// __int64 __fastcall json_object_iter_key(_QWORD); weak
// __int64 __fastcall json_object_key_to_iter(_QWORD); weak
// char *strtok(char *s, const char *delim);
// int __lxstat64(int ver, const char *filename, struct stat64 *stat_buf);
// void __noreturn exit(int status);
// size_t fwrite(const void *ptr, size_t size, size_t n, FILE *s);
// __int64 json_false(void); weak
// __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall json_object_iter(_QWORD); weak
// const unsigned __int16 **__ctype_b_loc(void);
// __int64 __fastcall json_object_get(_QWORD, _QWORD); weak
// __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD); weak
// __int64 __fastcall qs_io_read(_QWORD, _QWORD); weak
int __cdecl main(int argc, const char **argv, const char **envp);
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void));
__int64 sub_401D20();
void sub_401DA0();
__int64 sub_401E10();
char *__fastcall sub_401E40(const char *a1);
void __fastcall __noreturn sub_401EC0(const char *a1, int a2, const char *a3);
__int64 __fastcall sub_401EF0(const char *a1, const char *a2, int a3, __int64 a4);
const char *__fastcall trim(const char *a1);
__int64 __fastcall json_write_io(__int64 a1, __int64 a2, __int64 a3);
_QWORD *__fastcall qs_io_close(_QWORD *a1);
char **__fastcall translate_all(const char *a1, const char *a2, char *a3, int a4, __int64 a5);
void __fastcall __noreturn sub_402530(const char *a1);
__int64 __fastcall sub_402570(__int64 a1, __int64 a2, __int64 a3);
__int64 __fastcall sub_402590(__int64 a1);
__int64 __fastcall sub_4027E0(__int64 a1);
_BOOL8 __fastcall policy(unsigned int argc, char **argv);
void __fastcall __noreturn sub_4030E0(const char *a1);
_BOOL8 __fastcall message(int argc, char **argv);
void _libc_csu_init(void); // idb
void _libc_csu_fini(void); // idb
void (*sub_403AF0())(void);
void term_proc();

//-------------------------------------------------------------------------
// Data declarations

_UNKNOWN unk_40475E; // weak
__int64 qword_606000 = -1LL; // weak
__int64 qword_606010[] = { -1LL }; // weak
__int64 qword_606018 = 0LL; // weak
__int64 (*qword_606200)(void) = NULL; // weak
char *utilities = "policy"; // idb
__int64 (__fastcall *off_606428)(int argc, char **argv) = &policy; // weak
char *s2 = "message"; // idb
__int64 (__fastcall *off_606438)(int argc, char **argv) = &message; // weak
char *off_606440[2] = { "ENG", "en_US.UTF-8" }; // weak
struct option longopts = { "create", 0, NULL, 99 }; // idb
struct option stru_6067C0 = { "level", 1, NULL, 108 }; // idb
__int64 _bss_start; // weak
int optind; // weak
char *optarg; // idb
FILE *stderr; // idb
char byte_6069E8; // weak
__int64 qword_6069F0; // weak
char *s; // idb
__int64 qword_606A08; // weak
int dword_606A10; // weak
int dword_606A14; // weak
int dword_606A18; // weak
char *qword_606A20; // idb
char *qword_606A28; // idb
char *qword_606A30; // idb
char byte_606A38; // weak
char byte_606A39; // weak
char byte_606A3A; // weak
__int64 qword_606A40; // weak
char *filename; // idb
int dword_606A50; // weak
// extern _UNKNOWN _gmon_start__; weak


//----- (00000000004017C8) ----------------------------------------------------
void (*init_proc())(void)
{
  if ( &_gmon_start__ )
    __gmon_start__();
  sub_401E10();
  return sub_403AF0();
}
// 401A00: using guessed type __int64 __gmon_start__(void);

//----- (00000000004017F0) ----------------------------------------------------
__int64 sub_4017F0()
{
  return qword_606200();
}
// 4017F0: using guessed type __int64 __fastcall sub_4017F0();
// 606200: using guessed type __int64 (*qword_606200)(void);

//----- (0000000000401B90) ----------------------------------------------------
int __cdecl main(int argc, const char **argv, const char **envp)
{
  char *v3; // rax
  char *v4; // rbx
  __int64 (__fastcall *v5)(int, char **); // rax
  __int64 (__fastcall *v7)(int, char **); // rax

  v3 = basename(*argv);
  v4 = v3;
  if ( *(_WORD *)v3 == 25454 && !v3[2] )
  {
    if ( argc <= 1 )
    {
      fprintf(
        stderr,
        "\n"
        "[Notification Center] multi-call binary.\n"
        "\n"
        "Usage: %s [utility [arguments]...]\n"
        "\n"
        "    %s is a multi-call binary that combines many utilities.\n"
        "\n"
        "Currently defined utilities:\n"
        "\n"
        "    ",
        (const char *)_bss_start,
        (const char *)_bss_start);
      fprintf(stderr, "%s ", utilities);
      fprintf(stderr, "%s ", s2);
      fputc(10, stderr);
      exit(1);
    }
    v4 = basename(argv[1]);
    if ( !strcmp(v4, utilities) )
    {
      v5 = off_606428;
      return v5(argc - 1, (char **)argv + 1);
    }
    if ( !strcmp(v4, s2) )
    {
      v5 = off_606438;
      return v5(argc - 1, (char **)argv + 1);
    }
    goto LABEL_10;
  }
  if ( !strcmp(v3, utilities) )
  {
    v7 = off_606428;
  }
  else
  {
    if ( strcmp(v4, s2) )
    {
LABEL_10:
      fprintf(stderr, "[Notification Center] %s utility not found\n", v4);
      return 1;
    }
    v7 = off_606438;
  }
  return v7(argc, (char **)argv);
}
// 606428: using guessed type __int64 (__fastcall *off_606428)(int argc, char **argv);
// 606438: using guessed type __int64 (__fastcall *off_606438)(int argc, char **argv);
// 6069C0: using guessed type __int64 _bss_start;

//----- (0000000000401CF0) ----------------------------------------------------
// positive sp value has been detected, the output may be wrong!
void __fastcall __noreturn start(__int64 a1, __int64 a2, void (*a3)(void))
{
  __int64 v3; // rax
  int v4; // esi
  __int64 v5; // [rsp-8h] [rbp-8h] BYREF
  char *retaddr; // [rsp+0h] [rbp+0h] BYREF

  v4 = v5;
  v5 = v3;
  __libc_start_main(
    (int (__fastcall *)(int, char **, char **))main,
    v4,
    &retaddr,
    _libc_csu_init,
    _libc_csu_fini,
    a3,
    &v5);
  __halt();
}
// 401CF6: positive sp value 8 has been found
// 401CFD: variable 'v3' is possibly undefined

//----- (0000000000401D20) ----------------------------------------------------
__int64 sub_401D20()
{
  __int64 result; // rax

  result = 6318535LL - (_QWORD)&_bss_start;
  if ( (unsigned __int64)(6318535LL - (_QWORD)&_bss_start) > 0xE )
    return 0LL;
  return result;
}
// 6069C0: using guessed type __int64 _bss_start;

//----- (0000000000401DA0) ----------------------------------------------------
void sub_401DA0()
{
  __int64 v0; // rax
  unsigned __int64 i; // rbx

  if ( !byte_6069E8 )
  {
    v0 = qword_6069F0;
    for ( i = &qword_606018 - qword_606010 - 1; qword_6069F0 < i; v0 = qword_6069F0 )
    {
      qword_6069F0 = v0 + 1;
      ((void (*)(void))qword_606010[v0 + 1])();
    }
    sub_401D20();
    byte_6069E8 = 1;
  }
}
// 606010: using guessed type __int64 qword_606010[];
// 606018: using guessed type __int64 qword_606018;
// 6069E8: using guessed type char byte_6069E8;
// 6069F0: using guessed type __int64 qword_6069F0;

//----- (0000000000401E10) ----------------------------------------------------
__int64 sub_401E10()
{
  return 0LL;
}

//----- (0000000000401E40) ----------------------------------------------------
char *__fastcall sub_401E40(const char *a1)
{
  char v2[128]; // [rsp+0h] [rbp-1098h] BYREF
  char s[4120]; // [rsp+80h] [rbp-1018h] BYREF

  memset(v2, 0, sizeof(v2));
  memset(s, 0, 4097);
  snprintf(v2, 0x80uLL, "nc_%s", a1);
  snprintf(s, 0x1000uLL, "/mnt/ext/opt/NotificationCenter/share/apps/%s/.nc/locale", a1);
  bindtextdomain(v2, s);
  return textdomain(v2);
}

//----- (0000000000401EC0) ----------------------------------------------------
void __fastcall __noreturn sub_401EC0(const char *a1, int a2, const char *a3)
{
  fprintf(stderr, "%s\n%*s\n\n\x1B[;31;1m%s\x1B[0;m\n", a1, a2, "^", a3);
  exit(1);
}

//----- (0000000000401EF0) ----------------------------------------------------
__int64 __fastcall sub_401EF0(const char *a1, const char *a2, int a3, __int64 a4)
{
  const char *v4; // r13
  char v5; // al
  const char *v6; // rdx
  char *v7; // r14
  int v8; // ebx
  int v9; // eax
  const char *v10; // r12
  int v11; // edx
  char v12; // al
  const char *v13; // r13
  __int64 v14; // rax
  char *v16; // r13
  char v17; // bp
  size_t v18; // rax
  char *v19; // rax
  int v20; // esi
  char *v21; // rax
  char *v22; // rbp
  char *v23; // rax
  char **v24; // [rsp+8h] [rbp-1910h]
  char *msgid; // [rsp+20h] [rbp-18F8h]
  char *v28; // [rsp+30h] [rbp-18E8h]
  char *v29; // [rsp+38h] [rbp-18E0h]
  char s[128]; // [rsp+40h] [rbp-18D8h] BYREF
  char v31[2064]; // [rsp+C0h] [rbp-1858h] BYREF
  char dirname[4168]; // [rsp+8D0h] [rbp-1048h] BYREF

  v4 = a1;
  memset(v31, 0, 2049);
  v5 = *a1;
  if ( !*a1 )
  {
    v7 = v31;
    goto LABEL_29;
  }
  if ( v5 == 125 )
  {
    v20 = 1;
    goto LABEL_44;
  }
  v6 = a1;
  v7 = v31;
  while ( 1 )
  {
    v8 = 1;
    if ( v5 == 123 )
      break;
    if ( v7 - v31 > 2047 )
    {
LABEL_58:
      v13 = a1;
      goto LABEL_59;
    }
    *v7++ = v5;
    v5 = *++v6;
    if ( !v5 )
      goto LABEL_27;
LABEL_6:
    if ( v5 == 125 )
    {
      v4 = a1;
      v20 = (_DWORD)v6 - (_DWORD)a1 + 1;
      goto LABEL_44;
    }
  }
  while ( 1 )
  {
    v9 = *++v6;
    if ( *v6 != 123 )
      break;
    while ( (++v8 & 1) == 0 )
    {
      if ( v7 - v31 > 2047 )
        goto LABEL_58;
      v9 = *++v6;
      *v7++ = 123;
      if ( (_BYTE)v9 != 123 )
        goto LABEL_12;
    }
  }
LABEL_12:
  if ( (unsigned __int8)(v9 - 48) > 9u )
    sub_401EC0(a1, (_DWORD)v6 - (_DWORD)a1 + 1, "IndexError: index value only allow 0-9");
  if ( (v8 & 1) == 0 )
  {
    do
    {
      if ( v7 - v31 > 2047 )
        goto LABEL_58;
      ++v7;
      ++v6;
      *(v7 - 1) = v9;
      LOBYTE(v9) = *v6;
    }
    while ( (unsigned __int8)(*v6 - 48) <= 9u );
    v10 = v6;
    v11 = 0;
    if ( (v8 & 1) != 0 )
      goto LABEL_33;
LABEL_17:
    if ( (_BYTE)v9 == 125 )
      goto LABEL_18;
LABEL_43:
    v4 = a1;
    v20 = (_DWORD)v10 - (_DWORD)a1 + 1;
LABEL_44:
    sub_401EC0(v4, v20, "ValueError: encountered in format string");
  }
  v10 = v6;
  v11 = 0;
  do
  {
    ++v10;
    v11 = v9 + 10 * v11 - 48;
    v9 = *v10;
  }
  while ( (unsigned __int8)(*v10 - 48) <= 9u );
  if ( (v8 & 1) == 0 )
    goto LABEL_17;
LABEL_33:
  if ( a3 <= v11 || (v16 = *(char **)(a4 + 8LL * v11), v24 = (char **)(a4 + 8LL * v11), (v17 = *v16) == 0) )
    sub_401EC0(a1, (_DWORD)v10 - (_DWORD)a1, "IndexError: index out of range");
  v18 = strlen(v16);
  if ( v17 != 37 || v16[v18 - 1] != 37 )
    goto LABEL_36;
  if ( v18 <= 2 )
    goto LABEL_57;
  v21 = (char *)__strndup(v16 + 1, v18 - 2);
  msgid = v21;
  if ( !v21 )
    goto LABEL_57;
  v28 = strtok(v21, ":");
  v22 = strtok(0LL, ":");
  v29 = strtok(0LL, "");
  if ( !v22 )
    v22 = msgid;
  memset(s, 0, sizeof(s));
  memset(dirname, 0, 4097);
  snprintf(s, 0x80uLL, "nc_%s", v28);
  snprintf(dirname, 0x1000uLL, "/mnt/ext/opt/NotificationCenter/share/apps/%s/.nc/locale", v28);
  bindtextdomain(s, dirname);
  textdomain(s);
  setlocale(6, "");
  setenv("LANGUAGE", a2, 1);
  v23 = dcgettext(0LL, v22, 5);
  if ( v22 == v23 )
  {
    setlocale(6, "");
    setenv("LANGUAGE", "en_US.UTF-8", 1);
    v23 = dcgettext(0LL, v22, 5);
    if ( v22 == v23 )
    {
      v23 = v29;
      if ( !v29 )
LABEL_57:
        sub_401EC0(v16, 0, "VariableError: translate failure");
    }
  }
  v16 = (char *)__strdup(v23);
  free(msgid);
  v17 = *v16;
  if ( *v16 )
  {
LABEL_36:
    if ( v7 - v31 > 2047 )
LABEL_54:
      sub_401EC0(a1, (_DWORD)v10 - (_DWORD)a1 + 1, "LengthError: over string length 2048");
    v19 = v16;
    while ( 1 )
    {
      ++v7;
      ++v19;
      *(v7 - 1) = v17;
      v17 = *v19;
      if ( !*v19 )
        break;
      if ( v7 - v31 > 2047 )
        goto LABEL_54;
    }
  }
  if ( *v24 != v16 )
    free(v16);
  if ( *v10 != 125 )
    goto LABEL_43;
  do
  {
LABEL_18:
    if ( (v8 & 1) == 0 )
    {
      if ( v7 - v31 > 2047 )
        goto LABEL_54;
      *v7++ = 125;
    }
    if ( --v8 == -1 )
      goto LABEL_43;
    v12 = *++v10;
  }
  while ( *v10 == 125 );
  if ( v8 )
    goto LABEL_43;
  if ( v12 )
  {
    if ( v7 - v31 > 2047 )
      goto LABEL_54;
    *v7 = v12;
    v5 = v10[1];
    ++v7;
    v6 = v10 + 1;
    if ( !v5 )
    {
LABEL_27:
      v13 = a1;
      v14 = v7 - v31;
      goto LABEL_28;
    }
    goto LABEL_6;
  }
  v13 = a1;
  LODWORD(v6) = (_DWORD)v10;
  v14 = v7 - v31;
LABEL_28:
  if ( v14 > 2047 )
LABEL_59:
    sub_401EC0(v13, (_DWORD)v6 - (_DWORD)v13 + 1, "LengthError: over string length 2048");
LABEL_29:
  *v7 = 0;
  return __strdup(v31);
}
// 401980: using guessed type __int64 __fastcall __strdup(_QWORD);
// 4019D0: using guessed type __int64 __fastcall __strndup(_QWORD, _QWORD);

//----- (00000000004023F0) ----------------------------------------------------
const char *__fastcall trim(const char *a1)
{
  const char *v1; // rbx
  size_t v2; // rbp
  const unsigned __int16 *v3; // rax

  v1 = a1;
  v2 = strlen(a1);
  if ( v2 )
  {
    v3 = *__ctype_b_loc();
    do
    {
      if ( (v3[*v1] & 0x2000) == 0 )
        break;
      ++v1;
    }
    while ( v2 > v1 - a1 );
  }
  return v1;
}

//----- (0000000000402440) ----------------------------------------------------
__int64 __fastcall json_write_io(__int64 a1, __int64 a2, __int64 a3)
{
  return (unsigned int)qs_io_write(a3, a1, a2) - (unsigned int)a2;
}
// 401B30: using guessed type __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD);

//----- (0000000000402460) ----------------------------------------------------
_QWORD *__fastcall qs_io_close(_QWORD *a1)
{
  _QWORD *result; // rax
  __int64 v2; // rdi
  _QWORD *v3; // rbx

  result = a1;
  v2 = *a1;
  if ( v2 )
  {
    v3 = result;
    result = (_QWORD *)qs_io_free(v2);
    *v3 = 0LL;
  }
  return result;
}
// 401A60: using guessed type __int64 __fastcall qs_io_free(_QWORD);

//----- (0000000000402480) ----------------------------------------------------
char **__fastcall translate_all(const char *a1, const char *a2, char *a3, int a4, __int64 a5)
{
  char **i; // r12
  const char *v8; // rbp
  char *v9; // rax

  for ( i = off_606440; *i; *(i - 1) = (char *)sub_401EF0(v9, v8, a4, a5) )
  {
    sub_401E40(a1);
    setlocale(6, "");
    setenv("LANGUAGE", i[1], 1);
    v8 = i[1];
    v9 = dcgettext(0LL, a2, 5);
    if ( v9 == a2 )
      v9 = a3;
    i += 3;
  }
  return off_606440;
}
// 606440: using guessed type char *off_606440[2];

//----- (0000000000402530) ----------------------------------------------------
void __fastcall __noreturn sub_402530(const char *a1)
{
  const char *v1; // r8

  v1 = "%s\n%*s\n\n\x1B[;31;1m%s\x1B[0;m\n" + 24;
  if ( a1 )
    v1 = a1;
  fprintf(
    stderr,
    "\n"
    "[Notification Center] Policy Tool\n"
    "\n"
    "Usage: %s [action] [policy] [channel ...]\n"
    "\n"
    "[ACTION]:\n"
    "    -c, --create                   Create an application private policy\n"
    "\n"
    "[POLICY]:\n"
    "    -t, --type=TYPE                Policy type (2: notify)\n"
    "    -A, --app=ID                   Application ID\n"
    "    -C, --category=ID              Category ID (multipliable, default all)\n"
    "\n"
    "[CHANNEL]:\n"
    "    -m, --mail                     Auto pair mail\n"
    "    -s, --sms                      Auto pair SMS\n"
    "    -i, --im                       Auto pair IM\n"
    "    -p, --push                     Auto pair push notify\n"
    "\n"
    "Example:\n"
    "    %s -c -t2 -AA039 -CC004 --mail --push\n"
    "\n"
    "\x1B[;31;1m%s\x1B[0;m\n",
    "policy",
    "policy",
    v1);
  exit(1);
}

//----- (0000000000402570) ----------------------------------------------------
__int64 __fastcall sub_402570(__int64 a1, __int64 a2, __int64 a3)
{
  return (unsigned int)qs_io_write(a3, a1, a2) - (unsigned int)a2;
}
// 401B30: using guessed type __int64 __fastcall qs_io_write(_QWORD, _QWORD, _QWORD);

//----- (0000000000402590) ----------------------------------------------------
__int64 __fastcall sub_402590(__int64 a1)
{
  __int64 v1; // rbx
  __int64 v2; // rbp
  __int64 v3; // rax
  __int64 v4; // rax
  __int64 v5; // rbp
  __int64 v6; // rax
  __int64 v7; // rdi
  __int64 v8; // r12
  unsigned __int64 v9; // rbx
  __int64 v10; // rbp
  __int64 v11; // r13
  __int64 v12; // rax
  __int64 v13; // rax
  __int64 v14; // rax
  __int64 v15; // rax
  __int64 v16; // rax
  __int64 v17; // rax
  __int64 v18; // rax
  __int64 v20; // [rsp+8h] [rbp-40h] BYREF
  __int64 v21; // [rsp+10h] [rbp-38h] BYREF
  __int64 v22; // [rsp+18h] [rbp-30h]

  v1 = qs_io_create(a1);
  v2 = json_pack("{si}", "type", (unsigned int)a1);
  json_dump_callback(v2, sub_402570, v1, 5152LL);
  if ( v2 )
  {
    v3 = *(_QWORD *)(v2 + 8);
    if ( v3 != -1 )
    {
      v4 = v3 - 1;
      *(_QWORD *)(v2 + 8) = v4;
      if ( !v4 )
        json_delete(v2);
    }
  }
  if ( (unsigned int)qs_call("nc", "get_channel_receiver", v1, 60LL, 0xFFFFFFFFLL) )
  {
    fwrite("[Notification Center] is not avaiable.\n", 1uLL, 0x27uLL, stderr);
    exit(1);
  }
  v5 = qs_io_size(v1);
  v6 = qs_io_read(v1, 0LL);
  v7 = v1;
  v8 = json_loadb(v6, v5, 0LL, 0LL);
  v9 = 0LL;
  qs_io_free(v7);
  v20 = 0LL;
  json_unpack(v8, "{so}", "data", &v20);
  v10 = json_object();
  v11 = json_array(v8);
  while ( v9 < json_array_size(v20) )
  {
    v12 = json_array_get(v20, v9);
    if ( !v12 )
      break;
    v21 = 0LL;
    v22 = 0LL;
    if ( !(unsigned int)json_unpack(v12, "{sI ss}", "id", &v21) && !json_object_get(v10, v22) )
    {
      v13 = json_true();
      json_object_set_new(v10, v22, v13);
      v14 = json_integer(v21);
      json_array_append_new(v11, v14);
    }
    ++v9;
  }
  if ( v10 )
  {
    v15 = *(_QWORD *)(v10 + 8);
    if ( v15 != -1 )
    {
      v16 = v15 - 1;
      *(_QWORD *)(v10 + 8) = v16;
      if ( !v16 )
        json_delete(v10);
    }
  }
  if ( !v8 )
    return v11;
  v17 = *(_QWORD *)(v8 + 8);
  if ( v17 == -1 )
    return v11;
  v18 = v17 - 1;
  *(_QWORD *)(v8 + 8) = v18;
  if ( v18 )
    return v11;
  json_delete(v8);
  return v11;
}
// 401820: using guessed type __int64 __fastcall json_array(_QWORD);
// 401830: using guessed type __int64 __fastcall json_array_append_new(_QWORD, _QWORD);
// 401840: using guessed type __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD);
// 401850: using guessed type __int64 __fastcall json_integer(_QWORD);
// 401880: using guessed type __int64 json_object(void);
// 4018B0: using guessed type __int64 __fastcall json_delete(_QWORD);
// 401900: using guessed type __int64 __fastcall qs_io_create(_QWORD);
// 401940: using guessed type __int64 __fastcall json_array_get(_QWORD, _QWORD);
// 401950: using guessed type __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD);
// 401990: using guessed type __int64 json_true(void);
// 401A20: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 401A60: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 401A70: using guessed type __int64 __fastcall json_array_size(_QWORD);
// 401A90: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 401B10: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 401B20: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B60: using guessed type __int64 __fastcall json_object_get(_QWORD, _QWORD);
// 401B70: using guessed type __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B80: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);

//----- (00000000004027E0) ----------------------------------------------------
__int64 __fastcall sub_4027E0(__int64 a1)
{
  __int64 v1; // rbx
  __int64 v2; // rbp
  __int64 v3; // rax
  __int64 v4; // rax
  __int64 v5; // rbp
  __int64 v6; // rax
  __int64 v7; // rsi
  __int64 v8; // rbp
  __int64 v9; // rdi
  __int64 v10; // r12
  unsigned __int64 v11; // rbx
  __int64 v12; // rax
  __int64 result; // rax
  __int64 v14; // rdx
  __int64 v15; // rdx
  __int64 v16; // [rsp+8h] [rbp-40h]
  __int64 v17; // [rsp+20h] [rbp-28h] BYREF
  __int64 v18[4]; // [rsp+28h] [rbp-20h] BYREF

  v1 = qs_io_create(a1);
  v2 = json_pack("{si}", "type", (unsigned int)a1);
  json_dump_callback(v2, sub_402570, v1, 5152LL);
  if ( v2 )
  {
    v3 = *(_QWORD *)(v2 + 8);
    if ( v3 != -1 )
    {
      v4 = v3 - 1;
      *(_QWORD *)(v2 + 8) = v4;
      if ( !v4 )
        json_delete(v2);
    }
  }
  if ( (unsigned int)qs_call("nc", "get_channel_sender", v1, 60LL, 0xFFFFFFFFLL) )
  {
    fwrite("[Notification Center] is not avaiable.\n", 1uLL, 0x27uLL, stderr);
    exit(1);
  }
  v5 = qs_io_size(v1);
  v6 = qs_io_read(v1, 0LL);
  v7 = v5;
  v8 = 0LL;
  v9 = v1;
  v10 = json_loadb(v6, v7, 0LL, 0LL);
  v11 = 0LL;
  qs_io_free(v9);
  v17 = 0LL;
  json_unpack(v10, "{so}", "data", &v17);
  while ( v11 < json_array_size(v17) )
  {
    v12 = json_array_get(v17, v11);
    if ( !v12 )
      break;
    v18[0] = 0LL;
    if ( !(unsigned int)json_unpack(v12, "{sI sb}", "id", v18) && !v8 )
      v8 = v18[0];
    ++v11;
  }
  result = json_integer(v8);
  if ( v10 )
  {
    v14 = *(_QWORD *)(v10 + 8);
    if ( v14 != -1 )
    {
      v15 = v14 - 1;
      *(_QWORD *)(v10 + 8) = v15;
      if ( !v15 )
      {
        v16 = result;
        json_delete(v10);
        return v16;
      }
    }
  }
  return result;
}
// 402911: conditional instruction was optimized away because %var_2C.4==0
// 401840: using guessed type __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD);
// 401850: using guessed type __int64 __fastcall json_integer(_QWORD);
// 4018B0: using guessed type __int64 __fastcall json_delete(_QWORD);
// 401900: using guessed type __int64 __fastcall qs_io_create(_QWORD);
// 401940: using guessed type __int64 __fastcall json_array_get(_QWORD, _QWORD);
// 401950: using guessed type __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD);
// 401A60: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 401A70: using guessed type __int64 __fastcall json_array_size(_QWORD);
// 401A90: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 401B10: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 401B20: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B70: using guessed type __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B80: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 4027E0: using guessed type __int64 var_20[4];

//----- (00000000004029D0) ----------------------------------------------------
_BOOL8 __fastcall policy(unsigned int argc, char **argv)
{
  int v2; // eax
  __int64 v3; // rax
  char *v4; // rdi
  __int64 v5; // rax
  char v6; // dl
  char *v7; // r12
  __int64 v8; // rbp
  __int64 v9; // rbx
  __int64 v10; // r12
  __int64 v11; // rax
  __int64 v12; // r12
  int v13; // eax
  _BOOL4 v14; // ebx
  __int64 v15; // rdx
  __int64 v16; // rdx
  __int64 v17; // rdx
  __int64 v18; // rdx
  __int64 v20; // rax
  __int64 v21; // rax
  __int64 v22; // rax
  __int64 v23; // rax
  const char *v24; // rbp
  __int64 v25; // rbx
  __int64 v26; // r13
  __int64 v27; // rax
  __int64 v28; // r13
  __int64 v29; // rbx
  __int64 i; // rax
  __int64 v31; // rax
  __int64 v32; // rax
  __int64 v33; // rax
  __int64 v34; // rax
  __int64 v35; // rbp
  __int64 v36; // rax
  __int64 v37; // rax
  int v38; // [rsp+0h] [rbp-38h] BYREF
  int v39; // [rsp+4h] [rbp-34h] BYREF
  const char *v40; // [rsp+8h] [rbp-30h]

  v38 = 0;
  while ( 1 )
  {
    v2 = getopt_long(argc, argv, ":ct:A:C:msiph", &longopts, &v38);
    if ( v2 == -1 )
      break;
    switch ( v2 )
    {
      case 'A':
        s = optarg;
        break;
      case 'C':
        if ( !qword_606A08 )
          qword_606A08 = json_array(argc);
        v3 = json_string(optarg);
        json_array_append_new(qword_606A08, v3);
        break;
      case 'c':
        dword_606A10 = 1;
        break;
      case 'i':
        dword_606A18 |= 4u;
        break;
      case 'm':
        dword_606A18 |= 1u;
        break;
      case 'p':
        dword_606A18 |= 8u;
        break;
      case 's':
        dword_606A18 |= 2u;
        break;
      case 't':
        dword_606A14 = strtol(optarg, 0LL, 10);
        break;
      default:
        sub_402530(0LL);
    }
  }
  if ( dword_606A10 != 1 )
    sub_402530("incorrect action value !");
  if ( dword_606A14 != 2 )
    sub_402530("incorrect type value !");
  v4 = s;
  if ( !s || strlen(s) > 0x40 )
    sub_402530("incorrect app id value !");
  if ( (unsigned int)(dword_606A18 - 1) > 0xE )
    sub_402530("incorrect channel value !");
  v5 = json_array(v4);
  v6 = dword_606A18;
  v7 = (char *)v5;
  if ( (dword_606A18 & 1) != 0 )
  {
    sub_402590(1LL);
    sub_4027E0(1LL);
    v23 = json_pack("{si sb ss so so}", "type", 1LL);
    v4 = v7;
    json_array_append_new(v7, v23);
    v6 = dword_606A18;
  }
  if ( (v6 & 2) != 0 )
  {
    sub_402590(2LL);
    sub_4027E0(2LL);
    v22 = json_pack("{si sb ss so so}", "type", 2LL);
    v4 = v7;
    json_array_append_new(v7, v22);
    v6 = dword_606A18;
  }
  if ( (v6 & 4) != 0 )
  {
    sub_402590(4LL);
    v21 = json_pack("{si sb ss si so}", "type", 4LL);
    v4 = v7;
    json_array_append_new(v7, v21);
    v6 = dword_606A18;
  }
  if ( (v6 & 8) != 0 )
  {
    sub_402590(8LL);
    v20 = json_pack("{si sb ss si so}", "type", 8LL);
    v4 = v7;
    json_array_append_new(v7, v20);
  }
  if ( !qword_606A08 )
  {
    v24 = s;
    v25 = qs_io_create(v4);
    if ( (unsigned int)qs_call("nc", "get_app_support", v25, 60LL, 0xFFFFFFFFLL) )
    {
LABEL_60:
      fwrite("[Notification Center] is not avaiable.\n", 1uLL, 0x27uLL, stderr);
      exit(1);
    }
    v26 = qs_io_size(v25);
    v27 = qs_io_read(v25, 0LL);
    v28 = json_loadb(v27, v26, 0LL, 0LL);
    qs_io_free(v25);
    v40 = 0LL;
    if ( (unsigned int)json_unpack(v28, "{s{s{so}}}", "data", v24) )
    {
      fprintf(stderr, "[Notification Center] %s is not avaiable.\n", v24);
      exit(1);
    }
    v29 = json_array(v28);
    for ( i = json_object_iter(v40); ; i = json_object_iter_next(v40, v33) )
    {
      v34 = json_object_iter_key(i);
      v35 = v34;
      if ( !v34 )
        break;
      v31 = json_object_key_to_iter(v34);
      if ( !json_object_iter_value(v31) )
        break;
      v32 = json_string(v35);
      json_array_append_new(v29, v32);
      v33 = json_object_key_to_iter(v35);
    }
    if ( v28 )
    {
      v36 = *(_QWORD *)(v28 + 8);
      if ( v36 != -1 )
      {
        v37 = v36 - 1;
        *(_QWORD *)(v28 + 8) = v37;
        if ( !v37 )
          json_delete(v28);
      }
    }
  }
  v8 = json_pack(
         "{si ss ss s[{ss so}] s[{si sb si s[]} {si sb si s[]} {si sb si s[]}] so}",
         "type",
         (unsigned int)dword_606A14);
  v9 = qs_io_create("{si ss ss s[{ss so}] s[{si sb si s[]} {si sb si s[]} {si sb si s[]}] so}");
  json_dump_callback(v8, sub_402570, v9, 5152LL);
  if ( (unsigned int)qs_call("nc", "add_policy_notify", v9, 60LL, 0xFFFFFFFFLL) )
    goto LABEL_60;
  v10 = qs_io_size(v9);
  v11 = qs_io_read(v9, 0LL);
  v12 = json_loadb(v11, v10, 0LL, 0LL);
  qs_io_free(v9);
  v39 = 0;
  v40 = 0LL;
  if ( (unsigned int)json_unpack(v12, "{s?i s?s}", "error", &v39) )
  {
    fwrite("[Notification Center] JSON parser fail.\n", 1uLL, 0x28uLL, stderr);
    exit(1);
  }
  v13 = v39;
  if ( v39 && v40 )
  {
    fprintf(stderr, "[Notification Center] %s\n", v40);
    v13 = v39;
  }
  v14 = v13 != 0;
  if ( v12 )
  {
    v15 = *(_QWORD *)(v12 + 8);
    if ( v15 != -1 )
    {
      v16 = v15 - 1;
      *(_QWORD *)(v12 + 8) = v16;
      if ( !v16 )
        json_delete(v12);
    }
  }
  if ( v8 )
  {
    v17 = *(_QWORD *)(v8 + 8);
    if ( v17 != -1 )
    {
      v18 = v17 - 1;
      *(_QWORD *)(v8 + 8) = v18;
      if ( !v18 )
        json_delete(v8);
    }
  }
  return v14;
}
// 401810: using guessed type __int64 __fastcall json_object_iter_value(_QWORD);
// 401820: using guessed type __int64 __fastcall json_array(_QWORD);
// 401830: using guessed type __int64 __fastcall json_array_append_new(_QWORD, _QWORD);
// 401840: using guessed type __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD);
// 4018B0: using guessed type __int64 __fastcall json_delete(_QWORD);
// 401900: using guessed type __int64 __fastcall qs_io_create(_QWORD);
// 401920: using guessed type __int64 __fastcall json_object_iter_next(_QWORD, _QWORD);
// 401950: using guessed type __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD);
// 401A30: using guessed type __int64 __fastcall json_string(_QWORD);
// 401A60: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 401A90: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 401AA0: using guessed type __int64 __fastcall json_object_iter_key(_QWORD);
// 401AB0: using guessed type __int64 __fastcall json_object_key_to_iter(_QWORD);
// 401B10: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 401B20: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B40: using guessed type __int64 __fastcall json_object_iter(_QWORD);
// 401B70: using guessed type __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B80: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 606A08: using guessed type __int64 qword_606A08;
// 606A10: using guessed type int dword_606A10;
// 606A14: using guessed type int dword_606A14;
// 606A18: using guessed type int dword_606A18;

//----- (00000000004030E0) ----------------------------------------------------
void __fastcall __noreturn sub_4030E0(const char *a1)
{
  const char *v1; // rdx

  v1 = "%s\n%*s\n\n\x1B[;31;1m%s\x1B[0;m\n" + 24;
  if ( a1 )
    v1 = a1;
  fprintf(
    stderr,
    "\n"
    "[Notification Center] Message Tool\n"
    "\n"
    "Usage: message <MESSAGE...> <CHANNEL...> <default message> <variable...>\n"
    "\n"
    "[MESSAGE]:\n"
    "    -l, --level=LEVEL              Serverity level (1:info, 2:warning, 4:error)\n"
    "    -A, --app=ID                   Application ID\n"
    "    -C, --category=ID              Category ID\n"
    "    -M, --message=ID               Message ID (optional)\n"
    "    -a, --attachment FILE          email attachment file\n"
    "    -P, --purge                    purge attachment file after sent\n"
    "    -v, --verbose                  verbose\n"
    "    -d, --dry-run                  Only display translated message (optional)\n"
    "\n"
    "[CHANNEL]:\n"
    "        --mail=ID                  Created mail sender ID (default as system)\n"
    "        --mail-to=[mail,...]       Recipient mail addresses (multiple)\n"
    "        --sms=ID                   Created SMS sender ID (default as system)\n"
    "        --sms-to=[phone,...]       Recipient phone numbers (multiple)\n"
    "        --im-to=[ID,...]           Created IM receiver ID (multiple)\n"
    "        --push-to=[ID,...]         Created push-notify receiver ID (multiple)\n"
    "\n"
    "Example:\n"
    "    nc message -l2 -AA039 -CC004 '[App Center] The following updates are available for download: Notification Center"
    "' --mail=7686554474512384 --mail-to=<EMAIL> --mail-to=<EMAIL>\n"
    "    nc message -l2 -AA039 -CC004 -M59 '[{0}] The following updates are available for download: {1}' '%%A039:A039:App"
    " Center%%' '%%A008:A008:Notification Center%%' --mail-to=<EMAIL>\n"
    "\n"
    "\x1B[;31;1m%s\x1B[0;m\n",
    v1);
  exit(1);
}

//----- (0000000000403110) ----------------------------------------------------
_BOOL8 __fastcall message(int argc, char **argv)
{
  __int64 v4; // r12
  __int64 v5; // r13
  __int64 v6; // r14
  __int64 v7; // r15
  int v8; // eax
  __int64 v9; // rax
  __int64 v10; // rdi
  __int64 v11; // rax
  __int64 v12; // rax
  __int64 v13; // rdi
  __int64 v14; // rax
  __int64 v15; // rax
  __int64 v16; // rax
  __int64 v17; // rax
  __int64 v18; // rax
  char *v19; // rsi
  __int64 v20; // rax
  __int64 v21; // r13
  __int64 v22; // rax
  __int64 v23; // r13
  __int64 v24; // rax
  __int64 v25; // r13
  __int64 v26; // rax
  const char *v27; // rax
  char *v28; // r13
  char **v29; // rbx
  __int64 v30; // rbp
  char *v31; // rdi
  __int64 v32; // rax
  const char *v33; // rdi
  __int64 v34; // rbx
  __int64 v35; // rax
  __int64 v36; // rax
  __int64 v37; // r13
  __int64 v38; // rax
  __int64 v39; // rax
  __int64 v40; // rbx
  __int64 v41; // rax
  __int64 v42; // r14
  int v43; // eax
  _BOOL4 v44; // ebx
  __int64 v45; // rax
  __int64 v46; // rax
  __int64 v47; // rax
  __int64 v48; // rax
  __int64 v49; // rax
  __int64 v50; // rax
  const char *v52; // rdx
  const char *v53; // rsi
  __int64 v54; // [rsp+10h] [rbp-1140h]
  char *namea; // [rsp+18h] [rbp-1138h]
  char *nameb; // [rsp+18h] [rbp-1138h]
  char *name; // [rsp+18h] [rbp-1138h]
  __int64 v58; // [rsp+20h] [rbp-1130h]
  int longind; // [rsp+28h] [rbp-1128h] BYREF
  int v60; // [rsp+2Ch] [rbp-1124h] BYREF
  const char *v61; // [rsp+30h] [rbp-1120h]
  regex_t preg; // [rsp+38h] [rbp-1118h] BYREF
  struct stat64 stat_buf; // [rsp+78h] [rbp-10D8h] BYREF
  char resolved[4168]; // [rsp+108h] [rbp-1048h] BYREF

  v4 = json_pack("[{si sI s[]} {si sI s[]} {si sI s[]} {si sI s[]}]", "type", 1LL);
  v5 = json_array_get(v4, 0LL);
  v6 = json_array_get(v4, 1LL);
  v54 = json_array_get(v4, 2LL);
  longind = 0;
  v7 = json_array_get(v4, 3LL);
  while ( 2 )
  {
    v8 = getopt_long(argc, argv, ":l:A:C:M:a:Pvdh", &stru_6067C0, &longind);
    if ( v8 != -1 )
    {
      if ( v8 != 58 && v8 != 104 && v8 != 63 )
      {
        switch ( v8 )
        {
          case 1:
            v17 = strtoll(optarg, 0LL, 10);
            if ( !v17 )
              sub_4030E0("incorrect mail value !");
            v18 = json_integer(v17);
            json_object_set_new(v5, 4210447LL, v18);
            continue;
          case 2:
            if ( !optarg || !*optarg )
              sub_4030E0("incorrect mail-to value !");
            v13 = v5;
            goto LABEL_22;
          case 3:
            v15 = strtoll(optarg, 0LL, 10);
            if ( !v15 )
              sub_4030E0("incorrect sms value !");
            v16 = json_integer(v15);
            json_object_set_new(v6, 4210447LL, v16);
            continue;
          case 4:
            if ( !optarg || !*optarg )
              sub_4030E0("incorrect sms-to value !");
            v13 = v6;
LABEL_22:
            nameb = (char *)json_object_get(v13, 4210399LL);
            v14 = json_string(optarg);
            json_array_append_new(nameb, v14);
            continue;
          case 5:
            v12 = strtoll(optarg, 0LL, 10);
            if ( !v12 )
              sub_4030E0("incorrect im-to value !");
            v58 = v12;
            v10 = v54;
            goto LABEL_16;
          case 6:
            v9 = strtoll(optarg, 0LL, 10);
            if ( !v9 )
              sub_4030E0("incorrect push-to value !");
            v58 = v9;
            v10 = v7;
LABEL_16:
            namea = (char *)json_object_get(v10, 4210399LL);
            v11 = json_integer(v58);
            json_array_append_new(namea, v11);
            continue;
          case 65:
            qword_606A20 = optarg;
            continue;
          case 67:
            qword_606A28 = optarg;
            continue;
          case 77:
            qword_606A30 = optarg;
            continue;
          case 80:
            byte_606A38 = 1;
            continue;
          case 97:
            filename = optarg;
            continue;
          case 100:
            byte_606A39 = 1;
            continue;
          case 108:
            dword_606A50 = strtol(optarg, 0LL, 10);
            continue;
          case 118:
            byte_606A3A = 1;
            continue;
          default:
            break;
        }
      }
      sub_4030E0(0LL);
    }
    break;
  }
  memset(&stat_buf, 0, sizeof(stat_buf));
  memset(resolved, 0, 4097);
  regcomp(&preg, "^/share/(external/DEV[^/]+|[^/]+_DATA)/", 3);
  if ( (unsigned int)(dword_606A50 - 1) > 1 && dword_606A50 != 4 )
    sub_4030E0("incorrect level value !");
  if ( !qword_606A20 || strlen(qword_606A20) > 0x40 )
    sub_4030E0("incorrect app id value !");
  if ( !qword_606A28 || strlen(qword_606A28) > 0x40 )
    sub_4030E0("incorrect category id value !");
  if ( qword_606A30 && strlen(qword_606A30) > 0x40 )
    sub_4030E0("incorrect message id value !");
  if ( filename )
  {
    name = filename;
    if ( __lxstat64(1, filename, &stat_buf) || (stat_buf.st_mode & 0xF000) != 0x8000 || stat_buf.st_size > 26214400 )
      sub_4030E0("incorrect attachment value !");
    v19 = realpath(name, resolved);
    if ( !v19 || regexec(&preg, v19, 0LL, 0LL, 0) )
      sub_4030E0("incorrect attachment file !");
  }
  regfree(&preg);
  v20 = json_object_get(v5, 4210399LL);
  v21 = json_array_size(v20);
  v22 = json_object_get(v6, 4210399LL);
  v23 = json_array_size(v22) + v21;
  v24 = json_object_get(v54, 4210399LL);
  v25 = json_array_size(v24) + v23;
  v26 = json_object_get(v7, 4210399LL);
  if ( !(json_array_size(v26) + v25) )
    sub_4030E0("no any recevier !");
  if ( argc <= optind
    || (v27 = trim(argv[optind]), v28 = (char *)v27, (qword_606A40 = (__int64)v27) == 0)
    || !*v27
    || strlen(v27) > 0x7FF )
  {
    sub_4030E0("incorrect message context !");
  }
  v29 = translate_all(qword_606A20, qword_606A30, v28, argc - optind - 1, (__int64)&argv[optind + 1]);
  if ( byte_606A39 )
  {
    while ( 1 )
    {
      v53 = *v29;
      if ( !*v29 )
        break;
      v52 = v29[2];
      v29 += 3;
      printf("%s = %s\n", v53, v52);
      free(*(v29 - 1));
    }
    exit(0);
  }
  v30 = json_object();
  while ( *v29 )
  {
    v31 = v29[2];
    v29 += 3;
    v32 = json_string(v31);
    json_object_set_new(v30, *(v29 - 3), v32);
    free(*(v29 - 1));
  }
  v33 = "{si si sb ss ss ss sO sO}";
  v34 = json_pack("{si si sb ss ss ss sO sO}", "type", 2LL);
  if ( filename )
  {
    if ( byte_606A38 )
      v35 = json_true();
    else
      v35 = json_false();
    json_object_set_new(v34, "purge", v35);
    v36 = json_string(filename);
    v33 = (const char *)v34;
    json_object_set_new(v34, "attachment", v36);
  }
  v37 = qs_io_create(v33);
  json_dump_callback(v34, json_write_io, v37, 5152LL);
  if ( byte_606A3A )
  {
    json_dumpf(v34, stderr, 0LL);
    fputc(10, stderr);
  }
  if ( v34 )
  {
    v38 = *(_QWORD *)(v34 + 8);
    if ( v38 != -1 )
    {
      v39 = v38 - 1;
      *(_QWORD *)(v34 + 8) = v39;
      if ( !v39 )
        json_delete(v34);
    }
  }
  if ( (unsigned int)qs_call("nc", &unk_40475E, v37, 60LL, 0xFFFFFFFFLL) )
  {
    fwrite("[Notification Center] is not avaiable.\n", 1uLL, 0x27uLL, stderr);
    exit(1);
  }
  v40 = qs_io_size(v37);
  v41 = qs_io_read(v37, 0LL);
  v42 = json_loadb(v41, v40, 0LL, 0LL);
  v60 = 0;
  v61 = 0LL;
  if ( (unsigned int)json_unpack(v42, "{s?i s?s}", "error", &v60) )
  {
    fwrite("[Notification Center] JSON parser fail.\n", 1uLL, 0x28uLL, stderr);
    exit(1);
  }
  v43 = v60;
  if ( v60 && v61 )
  {
    fprintf(stderr, "[Notification Center] %s\n", v61);
    v43 = v60;
  }
  v44 = v43 != 0;
  if ( v37 )
    qs_io_free(v37);
  if ( v42 )
  {
    v45 = *(_QWORD *)(v42 + 8);
    if ( v45 != -1 )
    {
      v46 = v45 - 1;
      *(_QWORD *)(v42 + 8) = v46;
      if ( !v46 )
        json_delete(v42);
    }
  }
  if ( v30 )
  {
    v47 = *(_QWORD *)(v30 + 8);
    if ( v47 != -1 )
    {
      v48 = v47 - 1;
      *(_QWORD *)(v30 + 8) = v48;
      if ( !v48 )
        json_delete(v30);
    }
  }
  if ( v4 )
  {
    v49 = *(_QWORD *)(v4 + 8);
    if ( v49 != -1 )
    {
      v50 = v49 - 1;
      *(_QWORD *)(v4 + 8) = v50;
      if ( !v50 )
        json_delete(v4);
    }
  }
  return v44;
}
// 401830: using guessed type __int64 __fastcall json_array_append_new(_QWORD, _QWORD);
// 401840: using guessed type __int64 __fastcall json_pack(_QWORD, _QWORD, _QWORD);
// 401850: using guessed type __int64 __fastcall json_integer(_QWORD);
// 401880: using guessed type __int64 json_object(void);
// 401890: using guessed type __int64 __fastcall json_dumpf(_QWORD, _QWORD, _QWORD);
// 4018B0: using guessed type __int64 __fastcall json_delete(_QWORD);
// 401900: using guessed type __int64 __fastcall qs_io_create(_QWORD);
// 401940: using guessed type __int64 __fastcall json_array_get(_QWORD, _QWORD);
// 401950: using guessed type __int64 __fastcall json_loadb(_QWORD, _QWORD, _QWORD, _QWORD);
// 401990: using guessed type __int64 json_true(void);
// 401A20: using guessed type __int64 __fastcall json_object_set_new(_QWORD, _QWORD, _QWORD);
// 401A30: using guessed type __int64 __fastcall json_string(_QWORD);
// 401A60: using guessed type __int64 __fastcall qs_io_free(_QWORD);
// 401A70: using guessed type __int64 __fastcall json_array_size(_QWORD);
// 401A90: using guessed type __int64 __fastcall qs_io_size(_QWORD);
// 401B00: using guessed type __int64 json_false(void);
// 401B10: using guessed type __int64 __fastcall qs_call(_QWORD, _QWORD, _QWORD, _QWORD, _QWORD);
// 401B20: using guessed type __int64 __fastcall json_dump_callback(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B60: using guessed type __int64 __fastcall json_object_get(_QWORD, _QWORD);
// 401B70: using guessed type __int64 __fastcall json_unpack(_QWORD, _QWORD, _QWORD, _QWORD);
// 401B80: using guessed type __int64 __fastcall qs_io_read(_QWORD, _QWORD);
// 6069C8: using guessed type int optind;
// 606A38: using guessed type char byte_606A38;
// 606A39: using guessed type char byte_606A39;
// 606A3A: using guessed type char byte_606A3A;
// 606A40: using guessed type __int64 qword_606A40;
// 606A50: using guessed type int dword_606A50;

//----- (0000000000403A70) ----------------------------------------------------
void _libc_csu_init(void)
{
  init_proc();
}

//----- (0000000000403AF0) ----------------------------------------------------
void (*sub_403AF0())(void)
{
  void (**v0)(void); // rbx
  void (*result)(void); // rax

  v0 = (void (**)(void))&qword_606000;
  result = (void (*)(void))qword_606000;
  if ( qword_606000 != -1 )
  {
    do
    {
      --v0;
      result();
      result = *v0;
    }
    while ( *v0 != (void (*)(void))-1LL );
  }
  return result;
}
// 606000: using guessed type __int64 qword_606000;

//----- (0000000000403B28) ----------------------------------------------------
void term_proc()
{
  sub_401DA0();
}

// nfuncs=139 queued=24 decompiled=24 lumina nreq=0 worse=0 better=0
// ALL OK, 24 function(s) have been successfully decompiled
